# Figma Agent - Examples and Test Cases

## Example Figma Files for Testing

This document provides examples of Figma files that work well with Figma Agent, along with best practices for creating designs that generate high-quality code.

## Simple Examples

### 1. Basic Landing Page

**Description**: A simple landing page with header, hero section, and footer.

**Figma Structure**:
```
📁 Landing Page
├── 🖼️ Header
│   ├── 📝 Logo
│   └── 🔗 Navigation
│       ├── 📝 Home
│       ├── 📝 About
│       └── 📝 Contact
├── 🖼️ Hero Section
│   ├── 📝 Main Heading
│   ├── 📝 Subtitle
│   └── 🔘 CTA Button
└── 🖼️ Footer
    ├── 📝 Copyright
    └── 🔗 Social Links
```

**Best Practices Applied**:
- Semantic naming (Header, Hero Section, Footer)
- Auto Layout for navigation
- Proper text hierarchy
- Meaningful button names

**Expected Output**:
- Semantic HTML structure
- Responsive navigation
- Proper heading hierarchy (h1, h2, etc.)
- Accessible button with proper ARIA attributes

### 2. Contact Form

**Description**: A contact form with various input types and validation.

**Figma Structure**:
```
📁 Contact Form
├── 📝 Form Title
├── 🖼️ Name Field
│   ├── 📝 Name Label
│   └── ⬜ Name Input
├── 🖼️ Email Field
│   ├── 📝 Email Label
│   └── ⬜ Email Input
├── 🖼️ Message Field
│   ├── 📝 Message Label
│   └── ⬜ Message Textarea
└── 🔘 Submit Button
```

**Best Practices Applied**:
- Form field grouping
- Label-input associations
- Input type naming conventions
- Clear submit button

**Expected Output**:
- Proper form HTML structure
- Associated labels and inputs
- Appropriate input types
- Form validation attributes

### 3. Product Card Grid

**Description**: A responsive grid of product cards with images and details.

**Figma Structure**:
```
📁 Product Grid
├── 🖼️ Product Card (Component)
│   ├── 🖼️ Product Image
│   ├── 📝 Product Name
│   ├── 📝 Product Price
│   └── 🔘 Add to Cart
├── 🖼️ Grid Container (Auto Layout)
│   ├── 🔄 Product Card Instance
│   ├── 🔄 Product Card Instance
│   └── 🔄 Product Card Instance
```

**Best Practices Applied**:
- Component-based design
- Auto Layout for grid
- Consistent card structure
- Reusable components

**Expected Output**:
- CSS Grid or Flexbox layout
- Responsive card components
- Proper image handling
- Consistent styling

## Complex Examples

### 4. Dashboard Layout

**Description**: A complex dashboard with sidebar navigation, header, and content areas.

**Figma Structure**:
```
📁 Dashboard
├── 🖼️ Sidebar
│   ├── 📝 Logo
│   └── 🖼️ Navigation Menu
│       ├── 📝 Dashboard
│       ├── 📝 Analytics
│       ├── 📝 Users
│       └── 📝 Settings
├── 🖼️ Main Content
│   ├── 🖼️ Header Bar
│   │   ├── 📝 Page Title
│   │   └── 🖼️ User Profile
│   └── 🖼️ Content Area
│       ├── 🖼️ Stats Cards
│       └── 🖼️ Data Table
```

**Best Practices Applied**:
- Clear layout hierarchy
- Consistent navigation structure
- Modular content areas
- Proper spacing and alignment

**Expected Output**:
- CSS Grid layout for dashboard structure
- Sticky sidebar navigation
- Responsive content areas
- Proper semantic structure

### 5. E-commerce Product Page

**Description**: A detailed product page with image gallery, details, and purchase options.

**Figma Structure**:
```
📁 Product Page
├── 🖼️ Product Gallery
│   ├── 🖼️ Main Image
│   └── 🖼️ Thumbnail Strip
├── 🖼️ Product Info
│   ├── 📝 Product Title
│   ├── 📝 Price
│   ├── 📝 Description
│   ├── 🖼️ Size Selector
│   ├── 🖼️ Color Selector
│   └── 🔘 Add to Cart
└── 🖼️ Related Products
```

**Best Practices Applied**:
- Image gallery structure
- Interactive elements naming
- Product information hierarchy
- Related content sections

**Expected Output**:
- Image carousel/gallery
- Interactive selectors
- Structured product data
- Responsive layout

## Testing Scenarios

### Responsive Design Tests

1. **Mobile-First Design**:
   - Create mobile layout first
   - Use Auto Layout for stacking
   - Test with different screen sizes

2. **Tablet Breakpoint**:
   - Medium-width layouts
   - Adjusted spacing and sizing
   - Navigation adaptations

3. **Desktop Layout**:
   - Full-width designs
   - Multi-column layouts
   - Hover states and interactions

### Component Complexity Tests

1. **Simple Components**:
   - Buttons with text
   - Input fields
   - Basic cards

2. **Nested Components**:
   - Cards with multiple elements
   - Navigation with dropdowns
   - Forms with validation

3. **Complex Layouts**:
   - Multi-level navigation
   - Dashboard layouts
   - Grid systems

### Styling Tests

1. **Color Variations**:
   - Solid colors
   - Gradients
   - Transparency

2. **Typography**:
   - Multiple font weights
   - Different font sizes
   - Text alignment

3. **Effects**:
   - Drop shadows
   - Border radius
   - Opacity

## Common Patterns and Solutions

### Navigation Patterns

1. **Horizontal Navigation**:
   ```
   🖼️ Navigation (Auto Layout: Horizontal)
   ├── 📝 Home
   ├── 📝 About
   ├── 📝 Services
   └── 📝 Contact
   ```

2. **Vertical Sidebar**:
   ```
   🖼️ Sidebar (Auto Layout: Vertical)
   ├── 📝 Dashboard
   ├── 📝 Users
   ├── 📝 Settings
   └── 📝 Logout
   ```

### Form Patterns

1. **Stacked Form**:
   ```
   🖼️ Form (Auto Layout: Vertical)
   ├── 🖼️ Field Group
   │   ├── 📝 Label
   │   └── ⬜ Input
   └── 🔘 Submit
   ```

2. **Inline Form**:
   ```
   🖼️ Form (Auto Layout: Horizontal)
   ├── ⬜ Search Input
   └── 🔘 Search Button
   ```

### Card Patterns

1. **Basic Card**:
   ```
   🖼️ Card
   ├── 🖼️ Image
   ├── 📝 Title
   ├── 📝 Description
   └── 🔘 Action Button
   ```

2. **Stats Card**:
   ```
   🖼️ Stats Card
   ├── 📝 Value
   ├── 📝 Label
   └── 📊 Icon
   ```

## Troubleshooting Common Issues

### Design Issues

1. **Unnamed Layers**:
   - Problem: Layers named "Rectangle 1", "Frame 2"
   - Solution: Use descriptive names like "Hero Button", "Product Card"

2. **No Auto Layout**:
   - Problem: Manual positioning makes responsive design difficult
   - Solution: Apply Auto Layout to containers

3. **Inconsistent Spacing**:
   - Problem: Random spacing values
   - Solution: Use consistent spacing scale (8px, 16px, 24px, etc.)

### Generation Issues

1. **Complex Nested Structures**:
   - Problem: Too many nested groups
   - Solution: Flatten structure where possible

2. **Missing Text Content**:
   - Problem: Empty text layers
   - Solution: Add placeholder text

3. **Unsupported Effects**:
   - Problem: Complex blend modes or effects
   - Solution: Use simpler effects or convert to images

## Best Practices Summary

### Naming Conventions
- Use semantic names (Header, Navigation, Button)
- Be descriptive (Submit Button, Hero Image, Contact Form)
- Avoid generic names (Rectangle, Frame, Group)

### Layout Structure
- Use Auto Layout for flexible layouts
- Group related elements
- Maintain consistent hierarchy
- Apply proper constraints

### Component Design
- Create reusable components
- Use consistent styling
- Apply proper spacing
- Consider responsive behavior

### Content Preparation
- Add meaningful text content
- Use appropriate image placeholders
- Ensure proper contrast ratios
- Test with real content

## Testing Checklist

Before generating code from your Figma design:

- [ ] All layers have meaningful names
- [ ] Auto Layout is applied where appropriate
- [ ] Text content is present and readable
- [ ] Images are properly placed
- [ ] Colors have good contrast
- [ ] Spacing is consistent
- [ ] Components are reusable
- [ ] Structure is semantic
- [ ] Design works at different sizes
- [ ] Interactive elements are clearly defined

## Example URLs

Here are some example Figma files you can use for testing:

> **Note**: These are example URLs. Replace with actual Figma files you have access to.

1. **Simple Landing Page**: `https://www.figma.com/file/example1/Simple-Landing-Page`
2. **Contact Form**: `https://www.figma.com/file/example2/Contact-Form`
3. **Product Cards**: `https://www.figma.com/file/example3/Product-Cards`
4. **Dashboard Layout**: `https://www.figma.com/file/example4/Dashboard-Layout`

Remember to ensure you have access to these files and that they follow the best practices outlined in this document.
