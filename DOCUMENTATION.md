# Figma Agent - Complete Documentation

## Overview

Figma Agent is a comprehensive AI-powered application that converts Figma designs into full-fledged, production-ready websites. It leverages advanced AI capabilities through MyGenAssist LLM to generate high-quality code from Figma design files.

## Features

### 🎨 Design Processing
- **Figma API Integration**: Direct connection to Figma files via API
- **Intelligent Design Parsing**: Advanced parsing of Figma design elements
- **Component Recognition**: Automatic detection of UI components (buttons, inputs, navigation, etc.)
- **Layout Analysis**: Smart interpretation of Auto Layout and constraints

### 🤖 AI-Powered Code Generation
- **MyGenAssist Integration**: Uses your preferred LLM for enhanced code generation
- **Multiple Framework Support**: React, Vue, Angular
- **Styling Options**: CSS, SCSS, Tailwind CSS, Styled Components
- **TypeScript Support**: Full TypeScript integration with proper types
- **Responsive Design**: Mobile-first, responsive code generation
- **Accessibility**: ARIA attributes and semantic HTML

### 🖼️ Asset Management
- **Image Extraction**: Automatic extraction of images from Figma designs
- **Image Optimization**: Smart compression and format conversion (WebP)
- **Responsive Images**: Multiple breakpoint variants
- **Asset Organization**: Proper folder structure for assets

### 📦 Project Generation
- **Complete Project Structure**: Full project scaffolding
- **Package Management**: Proper dependency management
- **Configuration Files**: ESLint, Prettier, TypeScript configs
- **Build Scripts**: Ready-to-use build and development scripts
- **Documentation**: Auto-generated README files

## Getting Started

### Prerequisites

- Node.js (>= 16.0.0)
- npm (>= 8.0.0)
- Figma account with API access

### Installation

1. Clone or download the project
2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables in `.env`:
   ```env
   # MyGenAssist Configuration
   MYGENASSIST_API_TOKEN=your_mygenassist_token_here
   MYGENASSIST_API_URL=https://your-llm-api-endpoint.com/v1
   MYGENASSIST_MODEL=your-model-name

   # Figma Configuration
   FIGMA_TOKEN=your_figma_token_here
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

### Getting API Keys

#### Figma API Token
1. Go to [Figma Settings](https://www.figma.com/settings)
2. Scroll to "Personal access tokens"
3. Click "Create new token"
4. Give it a name and copy the token
5. Add it to your `.env` file as `FIGMA_TOKEN`

#### MyGenAssist API Token
1. Contact your MyGenAssist administrator
2. Request API access and obtain your token
3. Add it to your `.env` file as `MYGENASSIST_API_TOKEN`
4. Configure the API URL and model name

## Usage

### Basic Workflow

1. **Prepare Your Figma File**:
   - Ensure your design is well-organized
   - Use meaningful layer names
   - Apply Auto Layout where appropriate
   - Use components for reusable elements

2. **Generate Code**:
   - Open the Figma Agent application
   - Paste your Figma file URL
   - Enter your Figma access token
   - Configure generation options:
     - Framework (React, Vue, Angular)
     - Styling (CSS, SCSS, Tailwind, Styled Components)
     - TypeScript (enabled/disabled)
     - Responsive design (enabled/disabled)
     - Accessibility features (enabled/disabled)
   - Click "Generate Code"

3. **Download and Use**:
   - Download the generated ZIP file
   - Extract to your desired location
   - Run `npm install` to install dependencies
   - Run `npm start` to start the development server

### Supported Figma Features

- ✅ Frames and Groups
- ✅ Text layers with styling
- ✅ Shapes (Rectangle, Ellipse, etc.)
- ✅ Images and image fills
- ✅ Auto Layout (Flexbox)
- ✅ Constraints and positioning
- ✅ Colors and gradients
- ✅ Shadows and effects
- ✅ Corner radius and borders
- ✅ Component instances
- ✅ Nested structures

### Best Practices

#### Figma Design Tips
- **Use Auto Layout**: Enables better responsive code generation
- **Meaningful Names**: Use descriptive layer names (e.g., "Submit Button", "Hero Image")
- **Component Structure**: Group related elements into components
- **Consistent Spacing**: Use consistent padding and margins
- **Semantic Naming**: Use semantic names for sections (Header, Main, Footer)

#### Generated Code Tips
- **Review Generated Code**: Always review and test the generated code
- **Customize Styles**: Adjust colors, fonts, and spacing as needed
- **Add Functionality**: Implement event handlers and business logic
- **Optimize Images**: Replace placeholder images with actual assets
- **Test Responsiveness**: Test on different screen sizes

## Architecture

### Core Components

1. **Frontend (Next.js)**:
   - User interface for file upload and configuration
   - Real-time generation status
   - Error handling and validation

2. **API Layer**:
   - Figma API integration
   - MyGenAssist LLM communication
   - File processing and generation

3. **Design Parser**:
   - Figma node analysis
   - Component type detection
   - Style extraction
   - Layout interpretation

4. **Code Generator**:
   - Framework-specific code generation
   - Component creation
   - Style application
   - Project structure generation

5. **Asset Manager**:
   - Image extraction and processing
   - Optimization and compression
   - Responsive variant generation

### Technology Stack

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Backend**: Node.js, Next.js API Routes
- **AI Integration**: MyGenAssist LLM
- **Image Processing**: Sharp
- **File Processing**: JSZip
- **Testing**: Jest, React Testing Library

## Configuration

### Environment Variables

```env
# MyGenAssist Configuration
MYGENASSIST_API_TOKEN=your_token_here
MYGENASSIST_API_URL=https://api.example.com/v1
MYGENASSIST_MODEL=gpt-3.5-turbo

# Figma Configuration
FIGMA_TOKEN=your_figma_token_here

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development
```

### Generation Options

- **Framework**: `react` | `vue` | `angular`
- **Styling**: `css` | `scss` | `tailwind` | `styled-components`
- **TypeScript**: `true` | `false`
- **Responsive**: `true` | `false`
- **Accessibility**: `true` | `false`

## API Reference

### POST /api/generate

Generates code from a Figma design file.

**Request Body:**
```json
{
  "figmaUrl": "https://www.figma.com/file/...",
  "figmaToken": "figd_...",
  "options": {
    "framework": "react",
    "styling": "tailwind",
    "typescript": true,
    "responsive": true,
    "accessibility": true,
    "projectName": "my-project"
  }
}
```

**Response:**
- Success: ZIP file download
- Error: JSON error response

## Troubleshooting

### Common Issues

1. **Invalid Figma URL**:
   - Ensure the URL is from figma.com
   - Check that the file is not a community file
   - Verify you have access to the file

2. **API Token Issues**:
   - Verify your Figma token is valid
   - Check MyGenAssist token permissions
   - Ensure tokens are properly set in environment variables

3. **Generation Errors**:
   - Check that the Figma file has content
   - Verify the design is not too complex
   - Try with a simpler design first

4. **Build Issues**:
   - Run `npm install` to ensure dependencies are installed
   - Check for TypeScript errors with `npm run build`
   - Verify Node.js version compatibility

### Error Codes

- `INVALID_FIGMA_URL`: The provided Figma URL is invalid
- `INVALID_FIGMA_TOKEN`: The Figma access token is invalid
- `FIGMA_API_ERROR`: Error communicating with Figma API
- `CODE_GENERATION_ERROR`: Error during code generation
- `FILE_TOO_LARGE`: The Figma file is too large to process

## Contributing

### Development Setup

1. Fork the repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Run tests: `npm test`
5. Start development server: `npm run dev`

### Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- validation.test.ts
```

### Code Style

- ESLint configuration included
- Prettier for code formatting
- TypeScript for type safety
- Conventional commit messages

## License

MIT License - feel free to use this code in your projects.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the error messages carefully
3. Ensure all prerequisites are met
4. Test with a simple Figma design first

## Changelog

### v1.0.0
- Initial release
- Complete Figma to code generation
- Multiple framework support
- AI-powered enhancement
- Image asset processing
- Responsive design generation
- Accessibility features
- Comprehensive testing suite
