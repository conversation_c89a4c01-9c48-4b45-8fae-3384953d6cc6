import { FigmaClient } from '../figma-client';
import { DesignParser } from '../design-parser';
import { CodeGenerator } from '../code-generator';
import { ImageAssetManager } from '../image-asset-manager';
import { Validator } from '../validation';

// Mock data for testing
const mockFigmaFile = {
  name: 'Test Design',
  document: {
    id: '0:0',
    name: 'Document',
    type: 'DOCUMENT',
    children: [
      {
        id: '1:1',
        name: 'Page 1',
        type: 'CANVAS',
        children: [
          {
            id: '2:1',
            name: 'Main Container',
            type: 'FRAME',
            layoutMode: 'VERTICAL',
            paddingTop: 24,
            paddingRight: 24,
            paddingBottom: 24,
            paddingLeft: 24,
            itemSpacing: 16,
            fills: [{
              type: 'SOLID',
              color: { r: 1, g: 1, b: 1, a: 1 }
            }],
            absoluteBoundingBox: {
              x: 0,
              y: 0,
              width: 400,
              height: 300
            },
            children: [
              {
                id: '3:1',
                name: '<PERSON> <PERSON>',
                type: 'TEXT',
                characters: 'Welcome to Our App',
                style: {
                  fontSize: 32,
                  fontFamily: 'Inter',
                  fontWeight: 700,
                  fills: [{
                    type: 'SOLID',
                    color: { r: 0.1, g: 0.1, b: 0.1, a: 1 }
                  }]
                }
              },
              {
                id: '3:2',
                name: 'Description',
                type: 'TEXT',
                characters: 'This is a sample description text for our application.',
                style: {
                  fontSize: 16,
                  fontFamily: 'Inter',
                  fontWeight: 400,
                  fills: [{
                    type: 'SOLID',
                    color: { r: 0.4, g: 0.4, b: 0.4, a: 1 }
                  }]
                }
              },
              {
                id: '3:3',
                name: 'CTA Button',
                type: 'RECTANGLE',
                fills: [{
                  type: 'SOLID',
                  color: { r: 0.2, g: 0.4, b: 1, a: 1 }
                }],
                cornerRadius: 8,
                absoluteBoundingBox: {
                  x: 0,
                  y: 0,
                  width: 120,
                  height: 44
                },
                children: [{
                  id: '3:4',
                  name: 'Button Text',
                  type: 'TEXT',
                  characters: 'Get Started',
                  style: {
                    fontSize: 16,
                    fontFamily: 'Inter',
                    fontWeight: 500,
                    fills: [{
                      type: 'SOLID',
                      color: { r: 1, g: 1, b: 1, a: 1 }
                    }]
                  }
                }]
              }
            ]
          }
        ]
      }
    ]
  },
  components: {},
  styles: {}
};

describe('Integration Tests', () => {
  describe('End-to-End Workflow', () => {
    it('should process a complete Figma design workflow', async () => {
      // 1. Validate inputs
      const figmaUrl = 'https://www.figma.com/file/test123/Test-Design';
      const figmaToken = 'figd_test_token_1234567890abcdef';
      
      const urlValidation = Validator.validateFigmaUrl(figmaUrl);
      expect(urlValidation.isValid).toBe(true);
      
      const tokenValidation = Validator.validateFigmaToken(figmaToken);
      expect(tokenValidation.isValid).toBe(true);

      // 2. Parse Figma URL
      const urlInfo = Validator.parseFigmaUrl(figmaUrl);
      expect(urlInfo.fileId).toBe('test123');
      expect(urlInfo.isValidFormat).toBe(true);

      // 3. Parse design
      const parser = new DesignParser();
      const parsedComponent = parser.parse(mockFigmaFile.document);
      
      expect(parsedComponent).toBeDefined();
      expect(parsedComponent.children).toHaveLength(1); // Page 1
      
      const mainContainer = parsedComponent.children[0].children[0];
      expect(mainContainer.name).toBe('MainContainer');
      expect(mainContainer.children).toHaveLength(3); // Title, Description, Button

      // 4. Generate code
      const codeGenerator = new CodeGenerator({
        framework: 'react',
        styling: 'tailwind',
        typescript: true,
        responsive: true,
        accessibility: true,
        projectName: 'test-project',
      });

      const generatedProject = await codeGenerator.generateProject(parsedComponent);
      
      expect(generatedProject.files.length).toBeGreaterThan(0);
      expect(generatedProject.structure.framework).toBe('react');
      expect(generatedProject.structure.styling).toBe('tailwind');
      expect(generatedProject.structure.typescript).toBe(true);

      // 5. Verify generated files
      const packageJsonFile = generatedProject.files.find(f => f.path === 'package.json');
      expect(packageJsonFile).toBeDefined();
      
      const packageJson = JSON.parse(packageJsonFile!.content);
      expect(packageJson.name).toBe('test-project');
      expect(packageJson.dependencies).toBeDefined();
      expect(packageJson.devDependencies).toBeDefined();

      // 6. Verify component generation
      const appFile = generatedProject.files.find(f => f.path.includes('App.tsx'));
      expect(appFile).toBeDefined();
      expect(appFile!.content).toContain('import React');
      expect(appFile!.content).toContain('export default');

      // 7. Verify configuration files
      const tsConfigFile = generatedProject.files.find(f => f.path === 'tsconfig.json');
      expect(tsConfigFile).toBeDefined();
      
      const tailwindConfigFile = generatedProject.files.find(f => f.path === 'tailwind.config.js');
      expect(tailwindConfigFile).toBeDefined();
      
      const eslintConfigFile = generatedProject.files.find(f => f.path === '.eslintrc.json');
      expect(eslintConfigFile).toBeDefined();
    });

    it('should handle different framework configurations', async () => {
      const parser = new DesignParser();
      const parsedComponent = parser.parse(mockFigmaFile.document);

      // Test Vue configuration
      const vueGenerator = new CodeGenerator({
        framework: 'vue',
        styling: 'scss',
        typescript: false,
        responsive: true,
        accessibility: false,
        projectName: 'vue-project',
      });

      const vueProject = await vueGenerator.generateProject(parsedComponent);
      
      expect(vueProject.structure.framework).toBe('vue');
      expect(vueProject.structure.styling).toBe('scss');
      expect(vueProject.structure.typescript).toBe(false);

      const vueConfigFile = vueProject.files.find(f => f.path === 'vue.config.js');
      expect(vueConfigFile).toBeDefined();

      // Test Angular configuration
      const angularGenerator = new CodeGenerator({
        framework: 'angular',
        styling: 'css',
        typescript: true,
        responsive: false,
        accessibility: true,
        projectName: 'angular-project',
      });

      const angularProject = await angularGenerator.generateProject(parsedComponent);
      
      expect(angularProject.structure.framework).toBe('angular');
      expect(angularProject.structure.styling).toBe('css');
      expect(angularProject.structure.typescript).toBe(true);

      const angularConfigFile = angularProject.files.find(f => f.path === 'angular.json');
      expect(angularConfigFile).toBeDefined();
    });

    it('should handle image assets processing', async () => {
      // Mock Figma client for image processing
      const mockFigmaClient = {
        getImages: jest.fn().mockResolvedValue({
          images: {
            '3:5': 'https://figma.com/image1.png',
            '3:6': 'https://figma.com/image2.jpg'
          }
        }),
        downloadImage: jest.fn().mockResolvedValue(Buffer.from('mock-image-data'))
      } as any;

      const imageManager = new ImageAssetManager(mockFigmaClient, {
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 85,
        format: 'webp'
      });

      // Create a mock component with images
      const componentWithImages = {
        id: 'root',
        name: 'Root',
        type: 'CONTAINER' as any,
        props: {},
        children: [
          {
            id: '3:5',
            name: 'Hero Image',
            type: 'IMAGE' as any,
            props: { src: '/placeholder.jpg' },
            children: [],
            styles: {},
            layout: { isContainer: false }
          }
        ],
        styles: {},
        layout: { isContainer: true }
      };

      const assets = await imageManager.extractAndProcessImages('test-file', componentWithImages);
      
      expect(assets).toHaveLength(1);
      expect(assets[0].name).toContain('hero-image');
      expect(assets[0].format).toBe('webp');
      expect(mockFigmaClient.getImages).toHaveBeenCalledWith('test-file', ['3:5'], 'png', 2);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid Figma URLs gracefully', () => {
      const invalidUrls = [
        '',
        'not-a-url',
        'https://google.com',
        'https://figma.com/community/file/123/Design'
      ];

      invalidUrls.forEach(url => {
        const validation = Validator.validateFigmaUrl(url);
        expect(validation.isValid).toBe(false);
        expect(validation.errors.length).toBeGreaterThan(0);
      });
    });

    it('should handle malformed Figma data', () => {
      const parser = new DesignParser();
      
      // Test with missing required fields
      const malformedNode = {
        id: '1:1',
        // Missing name and type
      } as any;

      expect(() => parser.parse(malformedNode)).not.toThrow();
      
      const result = parser.parse(malformedNode);
      expect(result).toBeDefined();
      expect(result.name).toBeDefined(); // Should have fallback name
    });

    it('should validate generation options', () => {
      const invalidOptions = [
        { framework: 'invalid' },
        { styling: 'unknown' },
        { projectName: '' },
        { projectName: 'invalid@name!' },
        { typescript: 'not-boolean' }
      ];

      invalidOptions.forEach(options => {
        const validation = Validator.validateGenerationOptions(options);
        expect(validation.isValid).toBe(false);
        expect(validation.errors.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Performance Tests', () => {
    it('should handle large component trees efficiently', async () => {
      // Create a large nested component structure
      const createLargeTree = (depth: number, breadth: number): any => {
        if (depth === 0) {
          return {
            id: `leaf-${Math.random()}`,
            name: 'Leaf Node',
            type: 'TEXT',
            children: [],
            characters: 'Text content'
          };
        }

        const children = Array.from({ length: breadth }, () => 
          createLargeTree(depth - 1, breadth)
        );

        return {
          id: `node-${depth}-${Math.random()}`,
          name: `Container Level ${depth}`,
          type: 'FRAME',
          layoutMode: 'VERTICAL',
          children
        };
      };

      const largeTree = createLargeTree(4, 3); // 3^4 = 81 leaf nodes
      
      const startTime = Date.now();
      const parser = new DesignParser();
      const result = parser.parse(largeTree);
      const parseTime = Date.now() - startTime;

      expect(result).toBeDefined();
      expect(parseTime).toBeLessThan(1000); // Should parse in under 1 second

      // Test code generation performance
      const codeGenStart = Date.now();
      const generator = new CodeGenerator({
        framework: 'react',
        styling: 'tailwind',
        typescript: true,
        responsive: true,
        accessibility: true,
        projectName: 'performance-test',
      });

      const generatedProject = await generator.generateProject(result);
      const codeGenTime = Date.now() - codeGenStart;

      expect(generatedProject.files.length).toBeGreaterThan(0);
      expect(codeGenTime).toBeLessThan(5000); // Should generate in under 5 seconds
    });
  });
});
