import { FigmaClient } from '../figma-client';

describe('FigmaClient', () => {
  describe('extractFileKey', () => {
    it('should extract file key from valid Figma URL', () => {
      const url = 'https://www.figma.com/file/abc123def456/My-Design-File';
      const fileKey = FigmaClient.extractFileKey(url);
      expect(fileKey).toBe('abc123def456');
    });

    it('should return null for invalid URL', () => {
      const url = 'https://invalid-url.com';
      const fileKey = FigmaClient.extractFileKey(url);
      expect(fileKey).toBeNull();
    });

    it('should handle URLs with additional parameters', () => {
      const url = 'https://www.figma.com/file/abc123def456/My-Design-File?node-id=1%3A2';
      const fileKey = FigmaClient.extractFileKey(url);
      expect(fileKey).toBe('abc123def456');
    });
  });

  describe('extractNodeId', () => {
    it('should extract node ID from URL with node-id parameter', () => {
      const url = 'https://www.figma.com/file/abc123/Design?node-id=1%3A2';
      const nodeId = FigmaClient.extractNodeId(url);
      expect(nodeId).toBe('1:2');
    });

    it('should return null for URL without node-id parameter', () => {
      const url = 'https://www.figma.com/file/abc123/Design';
      const nodeId = FigmaClient.extractNodeId(url);
      expect(nodeId).toBeNull();
    });
  });

  describe('constructor', () => {
    it('should create instance with access token', () => {
      const token = 'test-token';
      const client = new FigmaClient(token);
      expect(client).toBeInstanceOf(FigmaClient);
    });
  });
});
