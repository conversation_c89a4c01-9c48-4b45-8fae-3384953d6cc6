import { Validator, ValidationError, FigmaApiError, CodeGenerationError } from '../validation';

describe('Validator', () => {
  describe('validateFigmaUrl', () => {
    it('should validate correct Figma URLs', () => {
      const validUrls = [
        'https://www.figma.com/file/abc123/My-Design',
        'https://figma.com/file/xyz789/Another-Design?node-id=1%3A2',
        'https://www.figma.com/file/test123/Design-File/duplicate',
      ];

      validUrls.forEach(url => {
        const result = Validator.validateFigmaUrl(url);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });
    });

    it('should reject invalid URLs', () => {
      const invalidUrls = [
        '',
        'not-a-url',
        'https://google.com',
        'https://www.figma.com/community/file/123/Design',
        'https://www.figma.com/invalid-path',
      ];

      invalidUrls.forEach(url => {
        const result = Validator.validateFigmaUrl(url);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });

    it('should warn about query parameters', () => {
      const url = 'https://www.figma.com/file/abc123/Design?param=value';
      const result = Validator.validateFigmaUrl(url);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('URL contains query parameters which will be ignored');
    });
  });

  describe('validateFigmaToken', () => {
    it('should validate correct tokens', () => {
      const validTokens = [
        'figd_abcdefghijklmnopqrstuvwxyz123456789',
        'abcdefghijklmnopqrstuvwxyz1234567890abcdef',
      ];

      validTokens.forEach(token => {
        const result = Validator.validateFigmaToken(token);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });
    });

    it('should reject invalid tokens', () => {
      const invalidTokens = [
        '',
        'short',
        '   ',
        null,
        undefined,
      ];

      invalidTokens.forEach(token => {
        const result = Validator.validateFigmaToken(token as any);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });
  });

  describe('validateGenerationOptions', () => {
    it('should validate correct options', () => {
      const validOptions = {
        framework: 'react',
        styling: 'tailwind',
        typescript: true,
        responsive: true,
        accessibility: true,
        projectName: 'my-project',
      };

      const result = Validator.validateGenerationOptions(validOptions);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid framework', () => {
      const options = { framework: 'invalid-framework' };
      const result = Validator.validateGenerationOptions(options);
      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('Invalid framework');
    });

    it('should reject invalid styling', () => {
      const options = { styling: 'invalid-styling' };
      const result = Validator.validateGenerationOptions(options);
      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('Invalid styling option');
    });

    it('should reject invalid project name', () => {
      const invalidNames = [
        '',
        '   ',
        'project with spaces and special chars!',
        'a'.repeat(51), // Too long
      ];

      invalidNames.forEach(name => {
        const options = { projectName: name };
        const result = Validator.validateGenerationOptions(options);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });
  });

  describe('parseFigmaUrl', () => {
    it('should extract file ID from URL', () => {
      const url = 'https://www.figma.com/file/abc123def456/My-Design';
      const result = Validator.parseFigmaUrl(url);
      
      expect(result.fileId).toBe('abc123def456');
      expect(result.isValidFormat).toBe(true);
      expect(result.isCommunityFile).toBe(false);
    });

    it('should detect community files', () => {
      const url = 'https://www.figma.com/community/file/123456/Community-Design';
      const result = Validator.parseFigmaUrl(url);
      
      expect(result.isCommunityFile).toBe(true);
    });

    it('should extract node ID from hash', () => {
      const url = 'https://www.figma.com/file/abc123/Design#node-id=1%3A2';
      const result = Validator.parseFigmaUrl(url);
      
      expect(result.nodeId).toBe('1:2');
    });
  });

  describe('sanitizeProjectName', () => {
    it('should sanitize project names', () => {
      const testCases = [
        ['My Project!', 'my-project'],
        ['Project   With   Spaces', 'project-with-spaces'],
        ['Special@#$%Characters', 'special-characters'],
        ['', 'figma-generated-project'],
        ['a'.repeat(60), 'a'.repeat(50)],
      ];

      testCases.forEach(([input, expected]) => {
        const result = Validator.sanitizeProjectName(input);
        expect(result).toBe(expected);
      });
    });
  });

  describe('validateFileSize', () => {
    it('should validate file sizes', () => {
      const smallFile = 1024 * 1024; // 1MB
      const result = Validator.validateFileSize(smallFile);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject large files', () => {
      const largeFile = 100 * 1024 * 1024; // 100MB
      const result = Validator.validateFileSize(largeFile);
      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('exceeds maximum allowed size');
    });

    it('should warn about moderately large files', () => {
      const moderateFile = 45 * 1024 * 1024; // 45MB (80% of 50MB default)
      const result = Validator.validateFileSize(moderateFile);
      expect(result.isValid).toBe(true);
      expect(result.warnings.length).toBeGreaterThan(0);
    });
  });

  describe('validateImageUrl', () => {
    it('should validate image URLs', () => {
      const validUrls = [
        'https://example.com/image.jpg',
        'https://figma.com/image.png',
        'https://cdn.example.com/photo.webp',
      ];

      validUrls.forEach(url => {
        const result = Validator.validateImageUrl(url);
        expect(result.isValid).toBe(true);
      });
    });

    it('should reject invalid URLs', () => {
      const invalidUrls = [
        '',
        'not-a-url',
        null,
        undefined,
      ];

      invalidUrls.forEach(url => {
        const result = Validator.validateImageUrl(url as any);
        expect(result.isValid).toBe(false);
      });
    });

    it('should warn about non-HTTPS URLs', () => {
      const url = 'http://example.com/image.jpg';
      const result = Validator.validateImageUrl(url);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Image URL should use HTTPS for security');
    });
  });
});

describe('Error Classes', () => {
  it('should create ValidationError correctly', () => {
    const error = new ValidationError('Test message', 'TEST_CODE', { detail: 'test' });
    expect(error.name).toBe('ValidationError');
    expect(error.message).toBe('Test message');
    expect(error.code).toBe('TEST_CODE');
    expect(error.details).toEqual({ detail: 'test' });
  });

  it('should create FigmaApiError correctly', () => {
    const error = new FigmaApiError('API Error', 404, { error: 'Not found' });
    expect(error.name).toBe('FigmaApiError');
    expect(error.message).toBe('API Error');
    expect(error.statusCode).toBe(404);
    expect(error.response).toEqual({ error: 'Not found' });
  });

  it('should create CodeGenerationError correctly', () => {
    const error = new CodeGenerationError('Generation failed', 'TestComponent', { reason: 'test' });
    expect(error.name).toBe('CodeGenerationError');
    expect(error.message).toBe('Generation failed');
    expect(error.component).toBe('TestComponent');
    expect(error.details).toEqual({ reason: 'test' });
  });
});
