import { DesignParser, ComponentType } from '../design-parser';
import { FigmaNode } from '../figma-client';

describe('DesignParser', () => {
  let parser: DesignParser;

  beforeEach(() => {
    parser = new DesignParser();
  });

  describe('parse', () => {
    it('should parse a simple text node', () => {
      const figmaNode: FigmaNode = {
        id: '1:1',
        name: 'Hello World',
        type: 'TEXT',
        characters: 'Hello World',
        style: {
          fontFamily: 'Inter',
          fontWeight: 400,
          fontSize: 16,
        },
      };

      const result = parser.parse(figmaNode);

      expect(result.type).toBe(ComponentType.TEXT);
      expect(result.props.text).toBe('Hello World');
      expect(result.styles.fontSize).toBe('16px');
      expect(result.styles.fontFamily).toBe('Inter');
    });

    it('should parse a button component', () => {
      const figmaNode: FigmaNode = {
        id: '1:2',
        name: '<PERSON> Button',
        type: 'FRAME',
        children: [{
          id: '1:3',
          name: 'Button Text',
          type: 'TEXT',
          characters: 'Click me',
        }],
        backgroundColor: { r: 0, g: 0.5, b: 1, a: 1 },
        cornerRadius: 8,
      };

      const result = parser.parse(figmaNode);

      expect(result.type).toBe(ComponentType.BUTTON);
      expect(result.styles.backgroundColor).toBe('rgb(0, 128, 255)');
      expect(result.styles.borderRadius).toBe('8px');
    });

    it('should parse a container with children', () => {
      const figmaNode: FigmaNode = {
        id: '1:4',
        name: 'Container',
        type: 'FRAME',
        layoutMode: 'VERTICAL',
        children: [
          {
            id: '1:5',
            name: 'Child 1',
            type: 'TEXT',
            characters: 'First child',
          },
          {
            id: '1:6',
            name: 'Child 2',
            type: 'TEXT',
            characters: 'Second child',
          },
        ],
        itemSpacing: 16,
      };

      const result = parser.parse(figmaNode);

      expect(result.type).toBe(ComponentType.CONTAINER);
      expect(result.children).toHaveLength(2);
      expect(result.styles.display).toBe('flex');
      expect(result.styles.flexDirection).toBe('column');
      expect(result.styles.gap).toBe('16px');
    });
  });

  describe('component type detection', () => {
    it('should detect heading from large font size', () => {
      const figmaNode: FigmaNode = {
        id: '1:7',
        name: 'Large Text',
        type: 'TEXT',
        characters: 'Main Title',
        style: {
          fontFamily: 'Inter',
          fontWeight: 700,
          fontSize: 32,
        },
      };

      const result = parser.parse(figmaNode);
      expect(result.type).toBe(ComponentType.HEADING);
    });

    it('should detect button from name', () => {
      const figmaNode: FigmaNode = {
        id: '1:8',
        name: 'Submit Button',
        type: 'FRAME',
      };

      const result = parser.parse(figmaNode);
      expect(result.type).toBe(ComponentType.BUTTON);
    });

    it('should detect input from name', () => {
      const figmaNode: FigmaNode = {
        id: '1:9',
        name: 'Email Input',
        type: 'FRAME',
      };

      const result = parser.parse(figmaNode);
      expect(result.type).toBe(ComponentType.INPUT);
    });
  });
});
