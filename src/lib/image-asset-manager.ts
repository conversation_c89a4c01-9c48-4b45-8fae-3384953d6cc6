import { FigmaClient, FigmaNode } from './figma-client';
import { ParsedComponent, ComponentType } from './design-parser';
import sharp from 'sharp';
import path from 'path';

export interface ImageAsset {
  id: string;
  name: string;
  url: string;
  buffer: Buffer;
  format: 'png' | 'jpg' | 'svg' | 'webp';
  width: number;
  height: number;
  optimized: boolean;
}

export interface ImageProcessingOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'png' | 'jpg' | 'webp';
  generateWebP?: boolean;
  generateResponsive?: boolean;
}

export class ImageAssetManager {
  private figmaClient: FigmaClient;
  private assets: Map<string, ImageAsset> = new Map();
  private processingOptions: ImageProcessingOptions;

  constructor(figmaClient: FigmaClient, options: ImageProcessingOptions = {}) {
    this.figmaClient = figmaClient;
    this.processingOptions = {
      maxWidth: 1920,
      maxHeight: 1080,
      quality: 85,
      format: 'webp',
      generateWebP: true,
      generateResponsive: true,
      ...options,
    };
  }

  async extractAndProcessImages(
    fileKey: string, 
    rootComponent: ParsedComponent
  ): Promise<ImageAsset[]> {
    // Find all image nodes in the component tree
    const imageNodes = this.findImageNodes(rootComponent);
    
    if (imageNodes.length === 0) {
      return [];
    }

    // Get image URLs from Figma API
    const nodeIds = imageNodes.map(node => node.id);
    const imageResponse = await this.figmaClient.getImages(fileKey, nodeIds, 'png', 2);
    
    if (!imageResponse.images) {
      console.warn('No images returned from Figma API');
      return [];
    }

    // Process each image
    const assets: ImageAsset[] = [];
    
    for (const node of imageNodes) {
      const imageUrl = imageResponse.images[node.id];
      if (!imageUrl) {
        console.warn(`No image URL for node ${node.id}`);
        continue;
      }

      try {
        const asset = await this.processImage(node, imageUrl);
        assets.push(asset);
        this.assets.set(asset.id, asset);
      } catch (error) {
        console.error(`Failed to process image for node ${node.id}:`, error);
      }
    }

    return assets;
  }

  private findImageNodes(component: ParsedComponent, imageNodes: ParsedComponent[] = []): ParsedComponent[] {
    // Check if current component is an image
    if (component.type === ComponentType.IMAGE || this.hasImageContent(component)) {
      imageNodes.push(component);
    }

    // Recursively check children
    for (const child of component.children) {
      this.findImageNodes(child, imageNodes);
    }

    return imageNodes;
  }

  private hasImageContent(component: ParsedComponent): boolean {
    // Check if component has image-related properties
    return !!(
      component.props.src ||
      component.styles.backgroundImage ||
      (component.styles.background && component.styles.background.includes('url('))
    );
  }

  private async processImage(node: ParsedComponent, imageUrl: string): Promise<ImageAsset> {
    // Download the image
    const imageBuffer = await this.figmaClient.downloadImage(imageUrl);
    
    // Get image metadata
    const metadata = await sharp(imageBuffer).metadata();
    const originalWidth = metadata.width || 0;
    const originalHeight = metadata.height || 0;

    // Generate a clean filename
    const filename = this.generateFilename(node.name, node.id);
    
    // Process and optimize the image
    const processedBuffer = await this.optimizeImage(imageBuffer);

    const asset: ImageAsset = {
      id: node.id,
      name: filename,
      url: imageUrl,
      buffer: processedBuffer,
      format: this.processingOptions.format || 'webp',
      width: originalWidth,
      height: originalHeight,
      optimized: true,
    };

    return asset;
  }

  private async optimizeImage(buffer: Buffer): Promise<Buffer> {
    let processor = sharp(buffer);

    // Resize if needed
    if (this.processingOptions.maxWidth || this.processingOptions.maxHeight) {
      processor = processor.resize(
        this.processingOptions.maxWidth,
        this.processingOptions.maxHeight,
        {
          fit: 'inside',
          withoutEnlargement: true,
        }
      );
    }

    // Convert format and optimize
    switch (this.processingOptions.format) {
      case 'webp':
        processor = processor.webp({ 
          quality: this.processingOptions.quality || 85,
          effort: 6 
        });
        break;
      case 'jpg':
        processor = processor.jpeg({ 
          quality: this.processingOptions.quality || 85,
          progressive: true 
        });
        break;
      case 'png':
        processor = processor.png({ 
          compressionLevel: 9,
          adaptiveFiltering: true 
        });
        break;
    }

    return processor.toBuffer();
  }

  private generateFilename(nodeName: string, nodeId: string): string {
    // Clean up the node name to create a valid filename
    const cleanName = nodeName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      || 'image';

    const shortId = nodeId.slice(-8);
    const extension = this.processingOptions.format || 'webp';
    
    return `${cleanName}-${shortId}.${extension}`;
  }

  async generateResponsiveImages(asset: ImageAsset): Promise<ImageAsset[]> {
    if (!this.processingOptions.generateResponsive) {
      return [asset];
    }

    const variants: ImageAsset[] = [asset];
    const breakpoints = [640, 768, 1024, 1280]; // Common responsive breakpoints

    for (const breakpoint of breakpoints) {
      if (breakpoint >= asset.width) continue;

      try {
        const resizedBuffer = await sharp(asset.buffer)
          .resize(breakpoint, null, {
            fit: 'inside',
            withoutEnlargement: true,
          })
          .toBuffer();

        const variant: ImageAsset = {
          ...asset,
          id: `${asset.id}-${breakpoint}w`,
          name: asset.name.replace(/\.([^.]+)$/, `-${breakpoint}w.$1`),
          buffer: resizedBuffer,
          width: breakpoint,
        };

        variants.push(variant);
      } catch (error) {
        console.error(`Failed to generate ${breakpoint}w variant:`, error);
      }
    }

    return variants;
  }

  getAsset(id: string): ImageAsset | undefined {
    return this.assets.get(id);
  }

  getAllAssets(): ImageAsset[] {
    return Array.from(this.assets.values());
  }

  updateComponentImagePaths(component: ParsedComponent, assetsPath: string = 'assets/images'): void {
    // Update image paths in the component tree
    this.updateImagePaths(component, assetsPath);
  }

  private updateImagePaths(component: ParsedComponent, assetsPath: string): void {
    const asset = this.assets.get(component.id);
    
    if (asset) {
      // Update image source path
      if (component.props.src) {
        component.props.src = `/${assetsPath}/${asset.name}`;
      }
      
      // Update background image path
      if (component.styles.backgroundImage) {
        component.styles.backgroundImage = `url(/${assetsPath}/${asset.name})`;
      }
    }

    // Recursively update children
    for (const child of component.children) {
      this.updateImagePaths(child, assetsPath);
    }
  }

  generateImageImports(assets: ImageAsset[]): string[] {
    return assets.map(asset => {
      const importName = this.generateImportName(asset.name);
      return `import ${importName} from '../assets/images/${asset.name}';`;
    });
  }

  private generateImportName(filename: string): string {
    return filename
      .replace(/\.[^.]+$/, '') // Remove extension
      .replace(/[^a-zA-Z0-9]/g, '_') // Replace non-alphanumeric with underscore
      .replace(/^[0-9]/, '_$&') // Prefix with underscore if starts with number
      .replace(/_+/g, '_') // Replace multiple underscores with single
      + 'Image';
  }
}
