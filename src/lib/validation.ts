export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface FigmaUrlInfo {
  fileId: string;
  nodeId?: string;
  isCommunityFile: boolean;
  isValidFormat: boolean;
}

export class ValidationError extends <PERSON>rror {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class FigmaApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public response?: any
  ) {
    super(message);
    this.name = 'FigmaApiError';
  }
}

export class CodeGenerationError extends Error {
  constructor(
    message: string,
    public component?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'CodeGenerationError';
  }
}

export class Validator {
  static validateFigmaUrl(url: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    if (!url || typeof url !== 'string') {
      result.isValid = false;
      result.errors.push('Figma URL is required');
      return result;
    }

    const trimmedUrl = url.trim();
    
    if (!trimmedUrl) {
      result.isValid = false;
      result.errors.push('Figma URL cannot be empty');
      return result;
    }

    // Check if it's a valid URL
    try {
      new URL(trimmedUrl);
    } catch {
      result.isValid = false;
      result.errors.push('Invalid URL format');
      return result;
    }

    // Check if it's a Figma URL
    if (!trimmedUrl.includes('figma.com')) {
      result.isValid = false;
      result.errors.push('URL must be from figma.com');
      return result;
    }

    const urlInfo = this.parseFigmaUrl(trimmedUrl);
    
    if (!urlInfo.isValidFormat) {
      result.isValid = false;
      result.errors.push('Invalid Figma URL format. Expected format: https://www.figma.com/file/[file-id]/[file-name]');
      return result;
    }

    if (urlInfo.isCommunityFile) {
      result.isValid = false;
      result.errors.push('Community files cannot be accessed via API. Please duplicate the file to your account and use the duplicated file URL.');
      return result;
    }

    if (!urlInfo.fileId) {
      result.isValid = false;
      result.errors.push('Could not extract file ID from URL');
      return result;
    }

    // Add warnings for common issues
    if (trimmedUrl.includes('?')) {
      result.warnings.push('URL contains query parameters which will be ignored');
    }

    if (trimmedUrl.includes('#')) {
      result.warnings.push('URL contains fragment which will be ignored');
    }

    return result;
  }

  static validateFigmaToken(token: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    if (!token || typeof token !== 'string') {
      result.isValid = false;
      result.errors.push('Figma access token is required');
      return result;
    }

    const trimmedToken = token.trim();
    
    if (!trimmedToken) {
      result.isValid = false;
      result.errors.push('Figma access token cannot be empty');
      return result;
    }

    // Basic format validation for Figma tokens
    if (trimmedToken.length < 20) {
      result.isValid = false;
      result.errors.push('Figma access token appears to be too short');
      return result;
    }

    // Check for common token format patterns
    const tokenPatterns = [
      /^figd_[a-zA-Z0-9_-]+$/, // Personal access token format
      /^[a-zA-Z0-9]{40,}$/, // Generic long token format
    ];

    const matchesPattern = tokenPatterns.some(pattern => pattern.test(trimmedToken));
    
    if (!matchesPattern) {
      result.warnings.push('Token format does not match expected Figma token patterns');
    }

    return result;
  }

  static validateGenerationOptions(options: any): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    if (!options || typeof options !== 'object') {
      result.isValid = false;
      result.errors.push('Generation options are required');
      return result;
    }

    // Validate framework
    const validFrameworks = ['react', 'vue', 'angular'];
    if (options.framework && !validFrameworks.includes(options.framework)) {
      result.isValid = false;
      result.errors.push(`Invalid framework. Must be one of: ${validFrameworks.join(', ')}`);
    }

    // Validate styling
    const validStyling = ['css', 'scss', 'tailwind', 'styled-components'];
    if (options.styling && !validStyling.includes(options.styling)) {
      result.isValid = false;
      result.errors.push(`Invalid styling option. Must be one of: ${validStyling.join(', ')}`);
    }

    // Validate project name
    if (options.projectName) {
      if (typeof options.projectName !== 'string') {
        result.isValid = false;
        result.errors.push('Project name must be a string');
      } else {
        const projectName = options.projectName.trim();
        if (!projectName) {
          result.isValid = false;
          result.errors.push('Project name cannot be empty');
        } else if (!/^[a-z0-9-_]+$/i.test(projectName)) {
          result.isValid = false;
          result.errors.push('Project name can only contain letters, numbers, hyphens, and underscores');
        } else if (projectName.length > 50) {
          result.isValid = false;
          result.errors.push('Project name cannot be longer than 50 characters');
        }
      }
    }

    // Validate boolean options
    const booleanOptions = ['typescript', 'responsive', 'accessibility'];
    for (const option of booleanOptions) {
      if (options[option] !== undefined && typeof options[option] !== 'boolean') {
        result.isValid = false;
        result.errors.push(`${option} must be a boolean value`);
      }
    }

    return result;
  }

  static parseFigmaUrl(url: string): FigmaUrlInfo {
    const result: FigmaUrlInfo = {
      fileId: '',
      nodeId: undefined,
      isCommunityFile: false,
      isValidFormat: false,
    };

    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;

      // Check if it's a community file
      result.isCommunityFile = pathname.includes('/community/file/');

      // Extract file ID from different URL formats
      const fileMatch = pathname.match(/\/file\/([a-zA-Z0-9]+)/);
      if (fileMatch) {
        result.fileId = fileMatch[1];
        result.isValidFormat = true;
      }

      // Extract node ID if present
      const nodeMatch = urlObj.hash.match(/node-id=([^&]+)/);
      if (nodeMatch) {
        result.nodeId = decodeURIComponent(nodeMatch[1]);
      }

    } catch (error) {
      // URL parsing failed
      result.isValidFormat = false;
    }

    return result;
  }

  static sanitizeProjectName(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9-_]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 50)
      || 'figma-generated-project';
  }

  static validateFileSize(size: number, maxSize: number = 50 * 1024 * 1024): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    if (size > maxSize) {
      result.isValid = false;
      result.errors.push(`File size (${Math.round(size / 1024 / 1024)}MB) exceeds maximum allowed size (${Math.round(maxSize / 1024 / 1024)}MB)`);
    } else if (size > maxSize * 0.8) {
      result.warnings.push(`File size is quite large (${Math.round(size / 1024 / 1024)}MB). Generation may take longer.`);
    }

    return result;
  }

  static validateImageUrl(url: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    if (!url || typeof url !== 'string') {
      result.isValid = false;
      result.errors.push('Image URL is required');
      return result;
    }

    try {
      const urlObj = new URL(url);
      
      // Check if it's HTTPS
      if (urlObj.protocol !== 'https:') {
        result.warnings.push('Image URL should use HTTPS for security');
      }

      // Check if it looks like an image URL
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
      const hasImageExtension = imageExtensions.some(ext => 
        urlObj.pathname.toLowerCase().includes(ext)
      );

      if (!hasImageExtension && !urlObj.hostname.includes('figma.com')) {
        result.warnings.push('URL does not appear to be an image file');
      }

    } catch {
      result.isValid = false;
      result.errors.push('Invalid image URL format');
    }

    return result;
  }
}

export function createErrorResponse(error: Error, statusCode: number = 500) {
  let message = 'An unexpected error occurred';
  let code = 'UNKNOWN_ERROR';
  let details = null;

  if (error instanceof ValidationError) {
    message = error.message;
    code = error.code;
    details = error.details;
    statusCode = 400;
  } else if (error instanceof FigmaApiError) {
    message = error.message;
    code = 'FIGMA_API_ERROR';
    statusCode = error.statusCode;
    details = error.response;
  } else if (error instanceof CodeGenerationError) {
    message = error.message;
    code = 'CODE_GENERATION_ERROR';
    details = { component: error.component, ...error.details };
    statusCode = 500;
  } else if (error.message.includes('fetch')) {
    message = 'Network error occurred';
    code = 'NETWORK_ERROR';
    statusCode = 503;
  }

  return {
    error: message,
    code,
    details,
    timestamp: new Date().toISOString(),
  };
}
