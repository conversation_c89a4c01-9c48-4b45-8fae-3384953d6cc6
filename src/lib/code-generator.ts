import { ParsedComponent, ComponentType, ComponentStyles } from './design-parser';
import { MyGenAssistClient } from './mygenassist-client';

export interface GeneratedProject {
  files: GeneratedFile[];
  structure: ProjectStructure;
  dependencies: Record<string, string>;
  devDependencies: Record<string, string>;
}

export interface GeneratedFile {
  path: string;
  content: string;
  type: 'component' | 'style' | 'config' | 'page' | 'layout';
}

export interface ProjectStructure {
  framework: 'react' | 'vue' | 'angular';
  styling: 'css' | 'scss' | 'tailwind' | 'styled-components';
  typescript: boolean;
  components: string[];
  pages: string[];
}

export interface CodeGenerationOptions {
  framework: 'react' | 'vue' | 'angular';
  styling: 'css' | 'scss' | 'tailwind' | 'styled-components';
  typescript: boolean;
  responsive: boolean;
  accessibility: boolean;
  projectName: string;
}

export class CodeGenerator {
  private options: CodeGenerationOptions;
  private generatedComponents: Set<string> = new Set();
  private myGenAssistClient: MyGenAssistClient;

  constructor(options: CodeGenerationOptions) {
    this.options = options;
    this.myGenAssistClient = new MyGenAssistClient();
  }

  async generateProject(rootComponent: ParsedComponent): Promise<GeneratedProject> {
    const files: GeneratedFile[] = [];
    const components: string[] = [];
    const pages: string[] = [];

    // Generate main component and its children
    const mainComponentFiles = await this.generateComponent(rootComponent, true);
    files.push(...mainComponentFiles);
    components.push(rootComponent.name);

    // Generate package.json
    files.push(this.generatePackageJson());

    // Generate configuration files
    files.push(...this.generateConfigFiles());

    // Generate global styles
    files.push(this.generateGlobalStyles());

    // Generate index/app file
    files.push(this.generateAppFile());
    pages.push('App');

    const dependencies = this.getDependencies();
    const devDependencies = this.getDevDependencies();

    return {
      files,
      structure: {
        framework: this.options.framework,
        styling: this.options.styling,
        typescript: this.options.typescript,
        components: Array.from(this.generatedComponents),
        pages,
      },
      dependencies,
      devDependencies,
    };
  }

  private async generateComponent(component: ParsedComponent, isRoot: boolean = false): Promise<GeneratedFile[]> {
    const files: GeneratedFile[] = [];

    if (this.generatedComponents.has(component.name)) {
      return files;
    }

    this.generatedComponents.add(component.name);

    switch (this.options.framework) {
      case 'react':
        files.push(await this.generateReactComponent(component, isRoot));
        break;
      case 'vue':
        files.push(this.generateVueComponent(component, isRoot));
        break;
      case 'angular':
        files.push(...this.generateAngularComponent(component, isRoot));
        break;
    }

    // Generate child components
    for (const child of component.children) {
      if (child.children.length > 0 || this.shouldGenerateAsComponent(child)) {
        files.push(...await this.generateComponent(child));
      }
    }

    return files;
  }

  private async generateReactComponent(component: ParsedComponent, isRoot: boolean): Promise<GeneratedFile> {
    const ext = this.options.typescript ? 'tsx' : 'jsx';
    const imports = this.generateReactImports(component);
    let componentCode = await this.generateReactComponentCode(component);
    const styles = this.generateComponentStyles(component);

    // Try to enhance with MyGenAssist if available
    try {
      const enhancedCode = await this.myGenAssistClient.enhanceCodeGeneration({
        componentData: component,
        framework: this.options.framework,
        styling: this.options.styling,
        typescript: this.options.typescript,
        responsive: this.options.responsive,
        accessibility: this.options.accessibility,
      });

      if (enhancedCode) {
        componentCode = enhancedCode;
      }
    } catch {
      console.log('Using basic code generation (MyGenAssist not available)');
    }

    let content = '';

    if (this.options.typescript) {
      content += `import React from 'react';\n`;
    } else {
      content += `import React from 'react';\n`;
    }

    content += imports;

    if (this.options.styling === 'styled-components') {
      content += `import styled from 'styled-components';\n`;
    } else if (this.options.styling === 'css' || this.options.styling === 'scss') {
      content += `import './${component.name}.${this.options.styling}';\n`;
    }

    content += '\n';
    content += componentCode;

    if (this.options.styling === 'styled-components') {
      content += '\n' + styles;
    }

    content += `\nexport default ${component.name};`;

    const path = isRoot ? `src/App.${ext}` : `src/components/${component.name}.${ext}`;

    return {
      path,
      content,
      type: isRoot ? 'page' : 'component',
    };
  }

  private generateReactImports(component: ParsedComponent): string {
    const imports: string[] = [];
    const childComponents = component.children
      .filter(child => this.shouldGenerateAsComponent(child))
      .map(child => child.name);

    for (const childName of childComponents) {
      imports.push(`import ${childName} from './${childName}';`);
    }

    return imports.length > 0 ? imports.join('\n') + '\n' : '';
  }

  private async generateReactComponentCode(component: ParsedComponent): Promise<string> {
    const props = this.options.typescript ?
      `interface ${component.name}Props {}\n\nconst ${component.name}: React.FC<${component.name}Props> = () => {` :
      `const ${component.name} = () => {`;

    const jsx = await this.generateJSX(component);

    return `${props}\n  return (\n${jsx}\n  );\n};`;
  }

  private async generateJSX(component: ParsedComponent, indent: number = 2): Promise<string> {
    const indentStr = ' '.repeat(indent);
    const tag = this.getHTMLTag(component);
    const className = this.generateClassName(component);
    const props = await this.generateJSXProps(component);
    const inlineStyles = this.generateInlineStyles(component);

    let opening = `${indentStr}<${tag}`;

    if (className) {
      opening += ` className="${className}"`;
    }

    if (inlineStyles && this.options.styling === 'css') {
      opening += ` style={${inlineStyles}}`;
    }

    if (props) {
      opening += ` ${props}`;
    }

    // Self-closing tags for certain elements
    if (this.isSelfClosingTag(tag) || (component.children.length === 0 && !component.props.text)) {
      return `${opening} />`;
    }

    opening += '>';

    let content = '';

    if (component.props.text) {
      // Escape text content for JSX
      content = this.escapeJSXText(component.props.text);
    } else {
      const childrenJSXPromises = component.children
        .map(async child => {
          if (this.shouldGenerateAsComponent(child)) {
            return `${indentStr}  <${child.name} />`;
          } else {
            return await this.generateJSX(child, indent + 2);
          }
        });

      const childrenJSX = await Promise.all(childrenJSXPromises);

      if (childrenJSX.length > 0) {
        content = `\n${childrenJSX.join('\n')}\n${indentStr}`;
      }
    }

    return `${opening}${content}</${tag}>`;
  }

  private getHTMLTag(component: ParsedComponent): string {
    // Map component types to appropriate HTML tags
    const tagMap: Record<ComponentType, string> = {
      [ComponentType.CONTAINER]: 'div',
      [ComponentType.TEXT]: 'span',
      [ComponentType.HEADING]: this.getHeadingTag(component),
      [ComponentType.BUTTON]: 'button',
      [ComponentType.INPUT]: 'input',
      [ComponentType.IMAGE]: 'img',
      [ComponentType.LINK]: 'a',
      [ComponentType.NAV]: 'nav',
      [ComponentType.HEADER]: 'header',
      [ComponentType.FOOTER]: 'footer',
      [ComponentType.MAIN]: 'main',
      [ComponentType.ASIDE]: 'aside',
      [ComponentType.ARTICLE]: 'article',
      [ComponentType.SECTION]: 'section',
      [ComponentType.LIST]: 'ul',
      [ComponentType.LIST_ITEM]: 'li',
    };

    return tagMap[component.type] || 'div';
  }

  private getHeadingTag(component: ParsedComponent): string {
    // Determine heading level based on font size or name
    const fontSize = component.styles.fontSize ? parseInt(component.styles.fontSize) : 16;
    const name = component.name.toLowerCase();

    if (name.includes('h1') || fontSize > 32) return 'h1';
    if (name.includes('h2') || fontSize > 28) return 'h2';
    if (name.includes('h3') || fontSize > 24) return 'h3';
    if (name.includes('h4') || fontSize > 20) return 'h4';
    if (name.includes('h5') || fontSize > 18) return 'h5';
    if (name.includes('h6') || fontSize > 16) return 'h6';

    return 'h2'; // Default to h2
  }

  private isSelfClosingTag(tag: string): boolean {
    const selfClosingTags = ['img', 'input', 'br', 'hr', 'meta', 'link'];
    return selfClosingTags.includes(tag);
  }

  private escapeJSXText(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/{/g, '&#x7B;')
      .replace(/}/g, '&#x7D;');
  }

  private generateInlineStyles(component: ParsedComponent): string | null {
    if (this.options.styling !== 'css' || Object.keys(component.styles).length === 0) {
      return null;
    }

    const styleEntries = Object.entries(component.styles)
      .map(([key, value]) => `${this.camelCaseProperty(key)}: '${value}'`)
      .join(', ');

    return `{{ ${styleEntries} }}`;
  }

  private camelCaseProperty(property: string): string {
    return property.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
  }

  private async generateJSXProps(component: ParsedComponent): Promise<string> {
    const props: string[] = [];

    Object.entries(component.props).forEach(([key, value]) => {
      if (key === 'text' || key === 'className') return;

      if (typeof value === 'string') {
        props.push(`${key}="${value}"`);
      } else {
        props.push(`${key}={${JSON.stringify(value)}}`);
      }
    });

    // Add accessibility attributes if enabled and MyGenAssist is available
    if (this.options.accessibility) {
      try {
        const a11yAttributes = await this.myGenAssistClient.generateAccessibilityAttributes(
          component.type,
          component.name
        );

        if (a11yAttributes) {
          Object.entries(a11yAttributes).forEach(([key, value]) => {
            props.push(`${key}="${value}"`);
          });
        }
      } catch {
        // Fallback to basic accessibility attributes
        this.addBasicA11yAttributes(component, props);
      }
    }

    return props.join(' ');
  }

  private addBasicA11yAttributes(component: ParsedComponent, props: string[]): void {
    switch (component.type) {
      case ComponentType.BUTTON:
        if (!component.props.text) {
          props.push('aria-label="Button"');
        }
        break;
      case ComponentType.INPUT:
        props.push('aria-required="false"');
        if (component.props.placeholder) {
          props.push(`aria-label="${component.props.placeholder}"`);
        }
        break;
      case ComponentType.IMAGE:
        if (!component.props.alt) {
          props.push('alt=""');
          props.push('role="presentation"');
        }
        break;
      case ComponentType.HEADING:
        props.push('role="heading"');
        break;
    }
  }

  private generateClassName(component: ParsedComponent): string {
    if (this.options.styling === 'tailwind') {
      return this.generateTailwindClasses(component.styles);
    } else {
      return component.name.toLowerCase();
    }
  }

  private generateTailwindClasses(styles: ComponentStyles): string {
    const classes: string[] = [];

    // Layout classes
    if (styles.display === 'flex') {
      classes.push('flex');
      if (styles.flexDirection === 'column') {
        classes.push('flex-col');
      } else if (styles.flexDirection === 'row') {
        classes.push('flex-row');
      }
    } else if (styles.display === 'block') {
      classes.push('block');
    } else if (styles.display === 'inline-block') {
      classes.push('inline-block');
    }

    // Justify content
    if (styles.justifyContent) {
      const justifyMap: Record<string, string> = {
        'center': 'justify-center',
        'flex-start': 'justify-start',
        'flex-end': 'justify-end',
        'space-between': 'justify-between',
        'space-around': 'justify-around',
        'space-evenly': 'justify-evenly',
      };
      if (justifyMap[styles.justifyContent]) {
        classes.push(justifyMap[styles.justifyContent]);
      }
    }

    // Align items
    if (styles.alignItems) {
      const alignMap: Record<string, string> = {
        'center': 'items-center',
        'flex-start': 'items-start',
        'flex-end': 'items-end',
        'stretch': 'items-stretch',
        'baseline': 'items-baseline',
      };
      if (alignMap[styles.alignItems]) {
        classes.push(alignMap[styles.alignItems]);
      }
    }

    // Width and height
    classes.push(...this.convertSizeToTailwind('w', styles.width));
    classes.push(...this.convertSizeToTailwind('h', styles.height));

    // Padding
    if (styles.padding) {
      classes.push(...this.convertPaddingToTailwind(styles.padding));
    }

    // Margin
    if (styles.margin) {
      classes.push(...this.convertMarginToTailwind(styles.margin));
    }

    // Gap
    if (styles.gap) {
      classes.push(...this.convertGapToTailwind(styles.gap));
    }

    // Background color
    if (styles.backgroundColor) {
      classes.push(...this.convertColorToTailwind('bg', styles.backgroundColor));
    }

    // Text color
    if (styles.color) {
      classes.push(...this.convertColorToTailwind('text', styles.color));
    }

    // Font size
    if (styles.fontSize) {
      classes.push(...this.convertFontSizeToTailwind(styles.fontSize));
    }

    // Font weight
    if (styles.fontWeight) {
      classes.push(...this.convertFontWeightToTailwind(styles.fontWeight));
    }

    // Text alignment
    if (styles.textAlign) {
      const textAlignMap: Record<string, string> = {
        'left': 'text-left',
        'center': 'text-center',
        'right': 'text-right',
        'justify': 'text-justify',
      };
      if (textAlignMap[styles.textAlign]) {
        classes.push(textAlignMap[styles.textAlign]);
      }
    }

    // Border radius
    if (styles.borderRadius) {
      classes.push(...this.convertBorderRadiusToTailwind(styles.borderRadius));
    }

    // Border
    if (styles.border) {
      classes.push(...this.convertBorderToTailwind(styles.border));
    }

    // Shadow
    if (styles.boxShadow) {
      classes.push(...this.convertShadowToTailwind(styles.boxShadow));
    }

    // Opacity
    if (styles.opacity) {
      classes.push(...this.convertOpacityToTailwind(styles.opacity));
    }

    // Add responsive classes if enabled
    if (this.options.responsive) {
      classes.push('w-full', 'max-w-full');
    }

    return classes.filter(Boolean).join(' ');
  }

  private convertSizeToTailwind(prefix: string, size?: string): string[] {
    if (!size) return [];

    const pxValue = parseInt(size);
    if (isNaN(pxValue)) {
      if (size === '100%') return [`${prefix}-full`];
      if (size === 'auto') return [`${prefix}-auto`];
      return [];
    }

    // Common size mappings
    const sizeMap: Record<number, string> = {
      4: '1', 8: '2', 12: '3', 16: '4', 20: '5', 24: '6', 28: '7', 32: '8',
      36: '9', 40: '10', 44: '11', 48: '12', 56: '14', 64: '16', 80: '20',
      96: '24', 112: '28', 128: '32', 144: '36', 160: '40', 192: '48',
      224: '56', 256: '64', 288: '72', 320: '80', 384: '96',
    };

    if (sizeMap[pxValue]) {
      return [`${prefix}-${sizeMap[pxValue]}`];
    }

    // For arbitrary values
    return [`${prefix}-[${size}]`];
  }

  private convertPaddingToTailwind(padding: string): string[] {
    const values = padding.split(' ').map(v => parseInt(v));
    const classes: string[] = [];

    if (values.length === 1) {
      const tailwindValue = this.pxToTailwindSpacing(values[0]);
      if (tailwindValue) classes.push(`p-${tailwindValue}`);
    } else if (values.length === 2) {
      const vertical = this.pxToTailwindSpacing(values[0]);
      const horizontal = this.pxToTailwindSpacing(values[1]);
      if (vertical) classes.push(`py-${vertical}`);
      if (horizontal) classes.push(`px-${horizontal}`);
    } else if (values.length === 4) {
      const [top, right, bottom, left] = values;
      const topTw = this.pxToTailwindSpacing(top);
      const rightTw = this.pxToTailwindSpacing(right);
      const bottomTw = this.pxToTailwindSpacing(bottom);
      const leftTw = this.pxToTailwindSpacing(left);

      if (topTw) classes.push(`pt-${topTw}`);
      if (rightTw) classes.push(`pr-${rightTw}`);
      if (bottomTw) classes.push(`pb-${bottomTw}`);
      if (leftTw) classes.push(`pl-${leftTw}`);
    }

    return classes;
  }

  private pxToTailwindSpacing(px: number): string | null {
    const spacingMap: Record<number, string> = {
      0: '0', 1: '0.5', 2: '0.5', 4: '1', 6: '1.5', 8: '2', 10: '2.5', 12: '3',
      14: '3.5', 16: '4', 20: '5', 24: '6', 28: '7', 32: '8', 36: '9', 40: '10',
      44: '11', 48: '12', 56: '14', 64: '16', 80: '20', 96: '24', 112: '28',
      128: '32', 144: '36', 160: '40', 192: '48', 224: '56', 256: '64',
    };

    return spacingMap[px] || null;
  }

  private generateComponentStyles(component: ParsedComponent): string {
    if (this.options.styling === 'styled-components') {
      return this.generateStyledComponents(component);
    } else if (this.options.styling === 'css' || this.options.styling === 'scss') {
      return this.generateCSSStyles(component);
    }
    return '';
  }

  private generateStyledComponents(component: ParsedComponent): string {
    const styles = Object.entries(component.styles)
      .map(([key, value]) => `  ${this.camelToKebab(key)}: ${value};`)
      .join('\n');

    return `const Styled${component.name} = styled.${component.type}\`\n${styles}\n\`;`;
  }

  private generateCSSStyles(component: ParsedComponent): string {
    const className = component.name.toLowerCase();
    const styles = Object.entries(component.styles)
      .map(([key, value]) => `  ${this.camelToKebab(key)}: ${value};`)
      .join('\n');

    return `.${className} {\n${styles}\n}`;
  }

  private camelToKebab(str: string): string {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
  }

  private convertMarginToTailwind(margin: string): string[] {
    // Similar to padding conversion
    const values = margin.split(' ').map(v => parseInt(v));
    const classes: string[] = [];

    if (values.length === 1) {
      const tailwindValue = this.pxToTailwindSpacing(values[0]);
      if (tailwindValue) classes.push(`m-${tailwindValue}`);
    } else if (values.length === 2) {
      const vertical = this.pxToTailwindSpacing(values[0]);
      const horizontal = this.pxToTailwindSpacing(values[1]);
      if (vertical) classes.push(`my-${vertical}`);
      if (horizontal) classes.push(`mx-${horizontal}`);
    }

    return classes;
  }

  private convertGapToTailwind(gap: string): string[] {
    const pxValue = parseInt(gap);
    const tailwindValue = this.pxToTailwindSpacing(pxValue);
    return tailwindValue ? [`gap-${tailwindValue}`] : [];
  }

  private convertColorToTailwind(prefix: string, color: string): string[] {
    // Basic color mapping - in a real implementation, you'd want more sophisticated color matching
    const colorMap: Record<string, string> = {
      'rgb(255, 255, 255)': 'white',
      'rgb(0, 0, 0)': 'black',
      'rgb(239, 68, 68)': 'red-500',
      'rgb(34, 197, 94)': 'green-500',
      'rgb(59, 130, 246)': 'blue-500',
      'rgb(168, 85, 247)': 'purple-500',
      'rgb(245, 158, 11)': 'yellow-500',
      'rgb(6, 182, 212)': 'cyan-500',
    };

    const tailwindColor = colorMap[color];
    if (tailwindColor) {
      return [`${prefix}-${tailwindColor}`];
    }

    // For arbitrary colors
    return [`${prefix}-[${color}]`];
  }

  private convertFontSizeToTailwind(fontSize: string): string[] {
    const pxValue = parseInt(fontSize);
    const fontSizeMap: Record<number, string> = {
      12: 'xs', 14: 'sm', 16: 'base', 18: 'lg', 20: 'xl',
      24: '2xl', 30: '3xl', 36: '4xl', 48: '5xl', 60: '6xl',
      72: '7xl', 96: '8xl', 128: '9xl',
    };

    const tailwindSize = fontSizeMap[pxValue];
    return tailwindSize ? [`text-${tailwindSize}`] : [`text-[${fontSize}]`];
  }

  private convertFontWeightToTailwind(fontWeight: string): string[] {
    const weightMap: Record<string, string> = {
      '100': 'thin', '200': 'extralight', '300': 'light', '400': 'normal',
      '500': 'medium', '600': 'semibold', '700': 'bold', '800': 'extrabold', '900': 'black',
    };

    const tailwindWeight = weightMap[fontWeight];
    return tailwindWeight ? [`font-${tailwindWeight}`] : [];
  }

  private convertBorderRadiusToTailwind(borderRadius: string): string[] {
    const pxValue = parseInt(borderRadius);
    const radiusMap: Record<number, string> = {
      0: 'none', 2: 'sm', 4: '', 6: 'md', 8: 'lg', 12: 'xl', 16: '2xl', 24: '3xl',
    };

    const tailwindRadius = radiusMap[pxValue];
    if (tailwindRadius !== undefined) {
      return tailwindRadius ? [`rounded-${tailwindRadius}`] : ['rounded'];
    }

    return [`rounded-[${borderRadius}]`];
  }

  private convertBorderToTailwind(border: string): string[] {
    const classes: string[] = [];

    // Parse border string (e.g., "1px solid rgb(0, 0, 0)")
    const borderParts = border.split(' ');
    if (borderParts.length >= 3) {
      const width = parseInt(borderParts[0]);
      const style = borderParts[1];
      const color = borderParts.slice(2).join(' ');

      // Border width
      if (width === 1) classes.push('border');
      else if (width === 2) classes.push('border-2');
      else if (width === 4) classes.push('border-4');
      else if (width === 8) classes.push('border-8');

      // Border style
      if (style === 'dashed') classes.push('border-dashed');
      else if (style === 'dotted') classes.push('border-dotted');

      // Border color
      classes.push(...this.convertColorToTailwind('border', color));
    }

    return classes;
  }

  private convertShadowToTailwind(boxShadow: string): string[] {
    // Basic shadow mapping
    const shadowMap: Record<string, string> = {
      '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)': 'shadow-sm',
      '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)': 'shadow',
      '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)': 'shadow-md',
      '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)': 'shadow-lg',
      '0 25px 50px -12px rgba(0, 0, 0, 0.25)': 'shadow-xl',
    };

    const tailwindShadow = shadowMap[boxShadow];
    return tailwindShadow ? [tailwindShadow] : ['shadow'];
  }

  private convertOpacityToTailwind(opacity: string): string[] {
    const opacityValue = parseFloat(opacity);
    const opacityMap: Record<number, string> = {
      0: '0', 0.05: '5', 0.1: '10', 0.2: '20', 0.25: '25', 0.3: '30',
      0.4: '40', 0.5: '50', 0.6: '60', 0.7: '70', 0.75: '75', 0.8: '80',
      0.9: '90', 0.95: '95', 1: '100',
    };

    const tailwindOpacity = opacityMap[opacityValue];
    return tailwindOpacity ? [`opacity-${tailwindOpacity}`] : [];
  }

  private shouldGenerateAsComponent(component: ParsedComponent): boolean {
    return component.children.length > 3 ||
           component.name.toLowerCase().includes('component') ||
           component.layout.isContainer && component.children.length > 0;
  }

  private generateVueComponent(component: ParsedComponent, isRoot: boolean): GeneratedFile {
    // Vue component generation logic would go here
    return {
      path: `src/components/${component.name}.vue`,
      content: `<!-- Vue component for ${component.name} -->`,
      type: isRoot ? 'page' : 'component',
    };
  }

  private generateAngularComponent(component: ParsedComponent, isRoot: boolean): GeneratedFile[] {
    // Angular component generation logic would go here
    return [{
      path: `src/app/components/${component.name.toLowerCase()}/${component.name.toLowerCase()}.component.ts`,
      content: `// Angular component for ${component.name}`,
      type: isRoot ? 'page' : 'component',
    }];
  }

  private generatePackageJson(): GeneratedFile {
    const packageJson = {
      name: this.options.projectName,
      version: '0.1.0',
      private: true,
      description: `Generated from Figma design using Figma Agent`,
      keywords: ['figma', 'generated', this.options.framework, this.options.styling],
      author: 'Figma Agent',
      license: 'MIT',
      scripts: this.getScripts(),
      dependencies: this.getDependencies(),
      devDependencies: this.getDevDependencies(),
      browserslist: this.getBrowsersList(),
      engines: {
        node: '>=16.0.0',
        npm: '>=8.0.0',
      },
    };

    return {
      path: 'package.json',
      content: JSON.stringify(packageJson, null, 2),
      type: 'config',
    };
  }

  private getScripts(): Record<string, string> {
    const baseScripts = {
      dev: 'npm run start',
      preview: 'npm run build && npm run serve',
      lint: 'eslint src --ext .js,.jsx,.ts,.tsx',
      'lint:fix': 'eslint src --ext .js,.jsx,.ts,.tsx --fix',
      format: 'prettier --write "src/**/*.{js,jsx,ts,tsx,json,css,scss,md}"',
      'format:check': 'prettier --check "src/**/*.{js,jsx,ts,tsx,json,css,scss,md}"',
    };

    switch (this.options.framework) {
      case 'react':
        return {
          ...baseScripts,
          start: 'react-scripts start',
          build: 'react-scripts build',
          test: 'react-scripts test',
          eject: 'react-scripts eject',
          serve: 'serve -s build',
        };
      case 'vue':
        return {
          ...baseScripts,
          start: 'vue-cli-service serve',
          build: 'vue-cli-service build',
          test: 'vue-cli-service test:unit',
          serve: 'vue-cli-service serve --mode production',
        };
      case 'angular':
        return {
          ...baseScripts,
          start: 'ng serve',
          build: 'ng build',
          test: 'ng test',
          e2e: 'ng e2e',
          serve: 'ng serve --configuration production',
        };
      default:
        return baseScripts;
    }
  }

  private getDependencies(): Record<string, string> {
    const baseDeps: Record<string, string> = {};

    switch (this.options.framework) {
      case 'react':
        baseDeps['react'] = '^18.2.0';
        baseDeps['react-dom'] = '^18.2.0';
        break;
      case 'vue':
        baseDeps['vue'] = '^3.3.0';
        baseDeps['vue-router'] = '^4.2.0';
        break;
      case 'angular':
        baseDeps['@angular/animations'] = '^16.0.0';
        baseDeps['@angular/common'] = '^16.0.0';
        baseDeps['@angular/compiler'] = '^16.0.0';
        baseDeps['@angular/core'] = '^16.0.0';
        baseDeps['@angular/forms'] = '^16.0.0';
        baseDeps['@angular/platform-browser'] = '^16.0.0';
        baseDeps['@angular/platform-browser-dynamic'] = '^16.0.0';
        baseDeps['@angular/router'] = '^16.0.0';
        baseDeps['rxjs'] = '^7.8.0';
        baseDeps['tslib'] = '^2.3.0';
        baseDeps['zone.js'] = '^0.13.0';
        break;
    }

    // Styling dependencies
    switch (this.options.styling) {
      case 'styled-components':
        baseDeps['styled-components'] = '^6.0.0';
        break;
      case 'tailwind':
        baseDeps['tailwindcss'] = '^3.3.0';
        baseDeps['autoprefixer'] = '^10.4.0';
        baseDeps['postcss'] = '^8.4.0';
        break;
      case 'scss':
        baseDeps['sass'] = '^1.63.0';
        break;
    }

    // Accessibility dependencies
    if (this.options.accessibility) {
      baseDeps['@axe-core/react'] = '^4.7.0';
    }

    return baseDeps;
  }

  private getDevDependencies(): Record<string, string> {
    const baseDeps: Record<string, string> = {
      'eslint': '^8.44.0',
      'prettier': '^3.0.0',
    };

    switch (this.options.framework) {
      case 'react':
        baseDeps['react-scripts'] = '^5.0.1';
        baseDeps['@testing-library/react'] = '^13.4.0';
        baseDeps['@testing-library/jest-dom'] = '^5.16.0';
        baseDeps['@testing-library/user-event'] = '^14.4.0';
        baseDeps['eslint-plugin-react'] = '^7.32.0';
        baseDeps['eslint-plugin-react-hooks'] = '^4.6.0';
        baseDeps['serve'] = '^14.2.0';
        break;
      case 'vue':
        baseDeps['@vue/cli-service'] = '^5.0.0';
        baseDeps['@vue/test-utils'] = '^2.4.0';
        baseDeps['eslint-plugin-vue'] = '^9.15.0';
        break;
      case 'angular':
        baseDeps['@angular-devkit/build-angular'] = '^16.0.0';
        baseDeps['@angular/cli'] = '^16.0.0';
        baseDeps['@angular/compiler-cli'] = '^16.0.0';
        baseDeps['jasmine-core'] = '^4.6.0';
        baseDeps['karma'] = '^6.4.0';
        baseDeps['karma-chrome-launcher'] = '^3.2.0';
        baseDeps['karma-coverage'] = '^2.2.0';
        baseDeps['karma-jasmine'] = '^5.1.0';
        baseDeps['karma-jasmine-html-reporter'] = '^2.1.0';
        break;
    }

    // TypeScript dependencies
    if (this.options.typescript) {
      baseDeps['typescript'] = '^5.1.0';
      baseDeps['@typescript-eslint/eslint-plugin'] = '^6.0.0';
      baseDeps['@typescript-eslint/parser'] = '^6.0.0';

      if (this.options.framework === 'react') {
        baseDeps['@types/react'] = '^18.2.0';
        baseDeps['@types/react-dom'] = '^18.2.0';
      }
    }

    // Styled components types
    if (this.options.styling === 'styled-components' && this.options.typescript) {
      baseDeps['@types/styled-components'] = '^5.1.0';
    }

    return baseDeps;
  }

  private getBrowsersList(): Record<string, string[]> {
    return {
      production: [
        '>0.2%',
        'not dead',
        'not op_mini all'
      ],
      development: [
        'last 1 chrome version',
        'last 1 firefox version',
        'last 1 safari version'
      ]
    };
  }

  private generateConfigFiles(): GeneratedFile[] {
    const files: GeneratedFile[] = [];

    // TypeScript configuration
    if (this.options.typescript) {
      files.push(this.generateTSConfig());
    }

    // Tailwind configuration
    if (this.options.styling === 'tailwind') {
      files.push(this.generateTailwindConfig());
      files.push(this.generatePostCSSConfig());
    }

    // ESLint configuration
    files.push(this.generateESLintConfig());

    // Prettier configuration
    files.push(this.generatePrettierConfig());

    // Environment files
    files.push(this.generateEnvFile());

    // Git ignore
    files.push(this.generateGitIgnore());

    // README
    files.push(this.generateProjectReadme());

    // Framework-specific configs
    switch (this.options.framework) {
      case 'vue':
        files.push(this.generateVueConfig());
        break;
      case 'angular':
        files.push(this.generateAngularConfig());
        break;
    }

    return files;
  }

  private generateTSConfig(): GeneratedFile {
    const config = {
      compilerOptions: {
        target: 'es5',
        lib: ['dom', 'dom.iterable', 'es6'],
        allowJs: true,
        skipLibCheck: true,
        esModuleInterop: true,
        allowSyntheticDefaultImports: true,
        strict: true,
        forceConsistentCasingInFileNames: true,
        noFallthroughCasesInSwitch: true,
        module: 'esnext',
        moduleResolution: 'node',
        resolveJsonModule: true,
        isolatedModules: true,
        noEmit: true,
        jsx: this.options.framework === 'react' ? 'react-jsx' : 'preserve',
        baseUrl: '.',
        paths: {
          '@/*': ['src/*'],
          '@/components/*': ['src/components/*'],
          '@/assets/*': ['src/assets/*'],
        },
      },
      include: [
        'src',
        'src/**/*',
      ],
      exclude: [
        'node_modules',
        'build',
        'dist',
      ],
    };

    return {
      path: 'tsconfig.json',
      content: JSON.stringify(config, null, 2),
      type: 'config',
    };
  }

  private generateTailwindConfig(): GeneratedFile {
    const config = `/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
    './public/index.html',
  ],
  theme: {
    extend: {
      colors: {
        // Add custom colors from your Figma design here
      },
      fontFamily: {
        // Add custom fonts from your Figma design here
      },
      spacing: {
        // Add custom spacing values here
      },
    },
  },
  plugins: [
    // Add Tailwind plugins as needed
    // require('@tailwindcss/forms'),
    // require('@tailwindcss/typography'),
  ],
};`;

    return {
      path: 'tailwind.config.js',
      content: config,
      type: 'config',
    };
  }

  private generatePostCSSConfig(): GeneratedFile {
    const config = `module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
};`;

    return {
      path: 'postcss.config.js',
      content: config,
      type: 'config',
    };
  }

  private generateESLintConfig(): GeneratedFile {
    const config = {
      env: {
        browser: true,
        es2021: true,
        node: true,
      },
      extends: [
        'eslint:recommended',
        ...(this.options.framework === 'react' ? [
          'plugin:react/recommended',
          'plugin:react-hooks/recommended',
        ] : []),
        ...(this.options.framework === 'vue' ? [
          'plugin:vue/vue3-essential',
        ] : []),
        ...(this.options.typescript ? [
          '@typescript-eslint/recommended',
        ] : []),
      ],
      parser: this.options.typescript ? '@typescript-eslint/parser' : undefined,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        ...(this.options.framework === 'react' ? {
          ecmaFeatures: {
            jsx: true,
          },
        } : {}),
      },
      plugins: [
        ...(this.options.framework === 'react' ? ['react'] : []),
        ...(this.options.framework === 'vue' ? ['vue'] : []),
        ...(this.options.typescript ? ['@typescript-eslint'] : []),
      ],
      rules: {
        'no-unused-vars': 'warn',
        'no-console': 'warn',
        ...(this.options.framework === 'react' ? {
          'react/react-in-jsx-scope': 'off',
          'react/prop-types': 'off',
        } : {}),
      },
      settings: {
        ...(this.options.framework === 'react' ? {
          react: {
            version: 'detect',
          },
        } : {}),
      },
    };

    return {
      path: '.eslintrc.json',
      content: JSON.stringify(config, null, 2),
      type: 'config',
    };
  }

  private generatePrettierConfig(): GeneratedFile {
    const config = {
      semi: true,
      trailingComma: 'es5',
      singleQuote: true,
      printWidth: 80,
      tabWidth: 2,
      useTabs: false,
      bracketSpacing: true,
      arrowParens: 'avoid',
      endOfLine: 'lf',
    };

    return {
      path: '.prettierrc.json',
      content: JSON.stringify(config, null, 2),
      type: 'config',
    };
  }

  private generateEnvFile(): GeneratedFile {
    let content = `# Environment variables for ${this.options.projectName}

# Development
NODE_ENV=development

# API URLs
# REACT_APP_API_URL=http://localhost:3001
# REACT_APP_API_KEY=your_api_key_here

# Feature flags
# REACT_APP_ENABLE_ANALYTICS=false
# REACT_APP_ENABLE_DEBUG=true
`;

    if (this.options.framework === 'vue') {
      content = content.replace(/REACT_APP_/g, 'VUE_APP_');
    } else if (this.options.framework === 'angular') {
      content = content.replace(/REACT_APP_/g, 'NG_APP_');
    }

    return {
      path: '.env.example',
      content,
      type: 'config',
    };
  }

  private generateGitIgnore(): GeneratedFile {
    const content = `# Dependencies
node_modules/
/.pnp
.pnp.js

# Production builds
/build
/dist
/out

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary folders
tmp/
temp/

# Framework specific
${this.options.framework === 'angular' ? `
# Angular
.angular/
` : ''}
${this.options.framework === 'vue' ? `
# Vue
.vue/
` : ''}
`;

    return {
      path: '.gitignore',
      content,
      type: 'config',
    };
  }

  private generateProjectReadme(): GeneratedFile {
    const content = `# ${this.options.projectName}

Generated from Figma design using Figma Agent.

## 🚀 Quick Start

### Prerequisites

- Node.js (>= 16.0.0)
- npm (>= 8.0.0)

### Installation

1. Clone or extract this project
2. Install dependencies:
   \`\`\`bash
   npm install
   \`\`\`

3. Start the development server:
   \`\`\`bash
   npm start
   \`\`\`

4. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## 📁 Project Structure

\`\`\`
${this.options.projectName}/
├── public/
│   ├── assets/
│   │   └── images/          # Generated image assets
│   └── index.html
├── src/
│   ├── components/          # React components
│   ├── assets/             # Static assets
│   └── index.${this.options.typescript ? 'tsx' : 'jsx'}
├── package.json
└── README.md
\`\`\`

## 🛠️ Available Scripts

- \`npm start\` - Start development server
- \`npm run build\` - Build for production
- \`npm test\` - Run tests
- \`npm run lint\` - Run ESLint
- \`npm run format\` - Format code with Prettier

## 🎨 Styling

This project uses **${this.options.styling}** for styling.

${this.options.styling === 'tailwind' ? `
### Tailwind CSS

Utility classes are used throughout the components. You can customize the design system in \`tailwind.config.js\`.
` : ''}

${this.options.styling === 'styled-components' ? `
### Styled Components

Components use CSS-in-JS with styled-components for styling.
` : ''}

## 🔧 Configuration

- **Framework**: ${this.options.framework}
- **TypeScript**: ${this.options.typescript ? 'Yes' : 'No'}
- **Responsive**: ${this.options.responsive ? 'Yes' : 'No'}
- **Accessibility**: ${this.options.accessibility ? 'Yes' : 'No'}

## 📝 Notes

- This is a generated project based on your Figma design
- Images are optimized and responsive variants are included
- Components follow accessibility best practices
- The code is formatted with Prettier and linted with ESLint

## 🤝 Contributing

1. Make your changes
2. Run \`npm run lint:fix\` to fix any linting issues
3. Run \`npm run format\` to format the code
4. Test your changes with \`npm test\`

## 📄 License

MIT License - feel free to use this code in your projects.
`;

    return {
      path: 'README.md',
      content,
      type: 'config',
    };
  }

  private generateVueConfig(): GeneratedFile {
    const config = `const { defineConfig } = require('@vue/cli-service');

module.exports = defineConfig({
  transpileDependencies: true,
  publicPath: process.env.NODE_ENV === 'production' ? '/' : '/',
  outputDir: 'dist',
  assetsDir: 'assets',
  lintOnSave: process.env.NODE_ENV !== 'production',
  productionSourceMap: false,
  devServer: {
    port: 3000,
    open: true,
  },
});`;

    return {
      path: 'vue.config.js',
      content: config,
      type: 'config',
    };
  }

  private generateAngularConfig(): GeneratedFile {
    const config = {
      $schema: './node_modules/@angular/cli/lib/config/schema.json',
      version: 1,
      newProjectRoot: 'projects',
      projects: {
        [this.options.projectName]: {
          projectType: 'application',
          schematics: {},
          root: '',
          sourceRoot: 'src',
          prefix: 'app',
          architect: {
            build: {
              builder: '@angular-devkit/build-angular:browser',
              options: {
                outputPath: 'dist',
                index: 'src/index.html',
                main: 'src/main.ts',
                polyfills: 'src/polyfills.ts',
                tsConfig: 'tsconfig.app.json',
                assets: ['src/favicon.ico', 'src/assets'],
                styles: ['src/styles.css'],
                scripts: [],
              },
            },
            serve: {
              builder: '@angular-devkit/build-angular:dev-server',
              options: {
                port: 3000,
              },
            },
            test: {
              builder: '@angular-devkit/build-angular:karma',
              options: {
                main: 'src/test.ts',
                polyfills: 'src/polyfills.ts',
                tsConfig: 'tsconfig.spec.json',
                karmaConfig: 'karma.conf.js',
                assets: ['src/favicon.ico', 'src/assets'],
                styles: ['src/styles.css'],
                scripts: [],
              },
            },
          },
        },
      },
    };

    return {
      path: 'angular.json',
      content: JSON.stringify(config, null, 2),
      type: 'config',
    };
  }

  private generateGlobalStyles(): GeneratedFile {
    const ext = this.options.styling === 'scss' ? 'scss' : 'css';
    let content = '';

    if (this.options.styling === 'tailwind') {
      content = `@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  body {
    @apply font-sans antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
  }
}

/* Custom component styles */
@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
}

/* Custom utility styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}`;
    } else {
      content = `/* Global Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: #333;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5em;
}

p {
  margin-bottom: 1em;
}

/* Links */
a {
  color: #007bff;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #0056b3;
  text-decoration: underline;
}

/* Buttons */
button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  transition: all 0.2s ease;
}

button:hover {
  opacity: 0.9;
}

button:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Form elements */
input, textarea, select {
  font-family: inherit;
  font-size: 1rem;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  transition: border-color 0.2s ease;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Images */
img {
  max-width: 100%;
  height: auto;
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}`;
    }

    return {
      path: `src/index.${ext}`,
      content,
      type: 'style',
    };
  }

  private generateAppFile(): GeneratedFile {
    const ext = this.options.typescript ? 'tsx' : 'jsx';
    
    const content = `import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.${this.options.styling === 'scss' ? 'scss' : 'css'}';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`;

    return {
      path: `src/index.${ext}`,
      content,
      type: 'page',
    };
  }
}
