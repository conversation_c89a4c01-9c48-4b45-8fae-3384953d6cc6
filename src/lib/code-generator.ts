import { ParsedComponent, ComponentType, ComponentStyles } from './design-parser';
import { MyGenAssistClient } from './mygenassist-client';

export interface GeneratedProject {
  files: GeneratedFile[];
  structure: ProjectStructure;
  dependencies: string[];
  devDependencies: string[];
}

export interface GeneratedFile {
  path: string;
  content: string;
  type: 'component' | 'style' | 'config' | 'page' | 'layout';
}

export interface ProjectStructure {
  framework: 'react' | 'vue' | 'angular';
  styling: 'css' | 'scss' | 'tailwind' | 'styled-components';
  typescript: boolean;
  components: string[];
  pages: string[];
}

export interface CodeGenerationOptions {
  framework: 'react' | 'vue' | 'angular';
  styling: 'css' | 'scss' | 'tailwind' | 'styled-components';
  typescript: boolean;
  responsive: boolean;
  accessibility: boolean;
  projectName: string;
}

export class CodeGenerator {
  private options: CodeGenerationOptions;
  private generatedComponents: Set<string> = new Set();
  private myGenAssistClient: MyGenAssistClient;

  constructor(options: CodeGenerationOptions) {
    this.options = options;
    this.myGenAssistClient = new MyGenAssistClient();
  }

  async generateProject(rootComponent: ParsedComponent): Promise<GeneratedProject> {
    const files: GeneratedFile[] = [];
    const components: string[] = [];
    const pages: string[] = [];

    // Generate main component and its children
    const mainComponentFiles = await this.generateComponent(rootComponent, true);
    files.push(...mainComponentFiles);
    components.push(rootComponent.name);

    // Generate package.json
    files.push(this.generatePackageJson());

    // Generate configuration files
    files.push(...this.generateConfigFiles());

    // Generate global styles
    files.push(this.generateGlobalStyles());

    // Generate index/app file
    files.push(this.generateAppFile());
    pages.push('App');

    const dependencies = this.getDependencies();
    const devDependencies = this.getDevDependencies();

    return {
      files,
      structure: {
        framework: this.options.framework,
        styling: this.options.styling,
        typescript: this.options.typescript,
        components: Array.from(this.generatedComponents),
        pages,
      },
      dependencies,
      devDependencies,
    };
  }

  private async generateComponent(component: ParsedComponent, isRoot: boolean = false): Promise<GeneratedFile[]> {
    const files: GeneratedFile[] = [];

    if (this.generatedComponents.has(component.name)) {
      return files;
    }

    this.generatedComponents.add(component.name);

    switch (this.options.framework) {
      case 'react':
        files.push(await this.generateReactComponent(component, isRoot));
        break;
      case 'vue':
        files.push(this.generateVueComponent(component, isRoot));
        break;
      case 'angular':
        files.push(...this.generateAngularComponent(component, isRoot));
        break;
    }

    // Generate child components
    for (const child of component.children) {
      if (child.children.length > 0 || this.shouldGenerateAsComponent(child)) {
        files.push(...await this.generateComponent(child));
      }
    }

    return files;
  }

  private async generateReactComponent(component: ParsedComponent, isRoot: boolean): Promise<GeneratedFile> {
    const ext = this.options.typescript ? 'tsx' : 'jsx';
    const imports = this.generateReactImports(component);
    let componentCode = await this.generateReactComponentCode(component);
    const styles = this.generateComponentStyles(component);

    // Try to enhance with MyGenAssist if available
    try {
      const enhancedCode = await this.myGenAssistClient.enhanceCodeGeneration({
        componentData: component,
        framework: this.options.framework,
        styling: this.options.styling,
        typescript: this.options.typescript,
        responsive: this.options.responsive,
        accessibility: this.options.accessibility,
      });

      if (enhancedCode) {
        componentCode = enhancedCode;
      }
    } catch {
      console.log('Using basic code generation (MyGenAssist not available)');
    }

    let content = '';

    if (this.options.typescript) {
      content += `import React from 'react';\n`;
    } else {
      content += `import React from 'react';\n`;
    }

    content += imports;

    if (this.options.styling === 'styled-components') {
      content += `import styled from 'styled-components';\n`;
    } else if (this.options.styling === 'css' || this.options.styling === 'scss') {
      content += `import './${component.name}.${this.options.styling}';\n`;
    }

    content += '\n';
    content += componentCode;

    if (this.options.styling === 'styled-components') {
      content += '\n' + styles;
    }

    content += `\nexport default ${component.name};`;

    const path = isRoot ? `src/App.${ext}` : `src/components/${component.name}.${ext}`;

    return {
      path,
      content,
      type: isRoot ? 'page' : 'component',
    };
  }

  private generateReactImports(component: ParsedComponent): string {
    const imports: string[] = [];
    const childComponents = component.children
      .filter(child => this.shouldGenerateAsComponent(child))
      .map(child => child.name);

    for (const childName of childComponents) {
      imports.push(`import ${childName} from './${childName}';`);
    }

    return imports.length > 0 ? imports.join('\n') + '\n' : '';
  }

  private async generateReactComponentCode(component: ParsedComponent): Promise<string> {
    const props = this.options.typescript ?
      `interface ${component.name}Props {}\n\nconst ${component.name}: React.FC<${component.name}Props> = () => {` :
      `const ${component.name} = () => {`;

    const jsx = await this.generateJSX(component);

    return `${props}\n  return (\n${jsx}\n  );\n};`;
  }

  private async generateJSX(component: ParsedComponent, indent: number = 2): Promise<string> {
    const indentStr = ' '.repeat(indent);
    const tag = component.type;
    const className = this.generateClassName(component);
    const props = await this.generateJSXProps(component);

    let opening = `${indentStr}<${tag}`;

    if (className) {
      opening += ` className="${className}"`;
    }

    if (props) {
      opening += ` ${props}`;
    }

    if (component.children.length === 0 && !component.props.text) {
      return `${opening} />`;
    }

    opening += '>';

    let content = '';

    if (component.props.text) {
      content = component.props.text;
    } else {
      const childrenJSXPromises = component.children
        .map(async child => {
          if (this.shouldGenerateAsComponent(child)) {
            return `${indentStr}  <${child.name} />`;
          } else {
            return await this.generateJSX(child, indent + 2);
          }
        });

      const childrenJSX = await Promise.all(childrenJSXPromises);

      if (childrenJSX.length > 0) {
        content = `\n${childrenJSX.join('\n')}\n${indentStr}`;
      }
    }

    return `${opening}${content}</${tag}>`;
  }

  private async generateJSXProps(component: ParsedComponent): Promise<string> {
    const props: string[] = [];

    Object.entries(component.props).forEach(([key, value]) => {
      if (key === 'text' || key === 'className') return;

      if (typeof value === 'string') {
        props.push(`${key}="${value}"`);
      } else {
        props.push(`${key}={${JSON.stringify(value)}}`);
      }
    });

    // Add accessibility attributes if enabled and MyGenAssist is available
    if (this.options.accessibility) {
      try {
        const a11yAttributes = await this.myGenAssistClient.generateAccessibilityAttributes(
          component.type,
          component.name
        );

        if (a11yAttributes) {
          Object.entries(a11yAttributes).forEach(([key, value]) => {
            props.push(`${key}="${value}"`);
          });
        }
      } catch {
        // Fallback to basic accessibility attributes
        this.addBasicA11yAttributes(component, props);
      }
    }

    return props.join(' ');
  }

  private addBasicA11yAttributes(component: ParsedComponent, props: string[]): void {
    switch (component.type) {
      case ComponentType.BUTTON:
        if (!component.props.text) {
          props.push('aria-label="Button"');
        }
        break;
      case ComponentType.INPUT:
        props.push('aria-required="false"');
        if (component.props.placeholder) {
          props.push(`aria-label="${component.props.placeholder}"`);
        }
        break;
      case ComponentType.IMAGE:
        if (!component.props.alt) {
          props.push('alt=""');
          props.push('role="presentation"');
        }
        break;
      case ComponentType.HEADING:
        props.push('role="heading"');
        break;
    }
  }

  private generateClassName(component: ParsedComponent): string {
    if (this.options.styling === 'tailwind') {
      return this.generateTailwindClasses(component.styles);
    } else {
      return component.name.toLowerCase();
    }
  }

  private generateTailwindClasses(styles: ComponentStyles): string {
    const classes: string[] = [];
    
    // Convert styles to Tailwind classes
    if (styles.display === 'flex') {
      classes.push('flex');
      if (styles.flexDirection === 'column') {
        classes.push('flex-col');
      }
    }
    
    if (styles.justifyContent) {
      const justifyMap: Record<string, string> = {
        'center': 'justify-center',
        'flex-start': 'justify-start',
        'flex-end': 'justify-end',
        'space-between': 'justify-between',
        'space-around': 'justify-around',
      };
      if (justifyMap[styles.justifyContent]) {
        classes.push(justifyMap[styles.justifyContent]);
      }
    }
    
    if (styles.alignItems) {
      const alignMap: Record<string, string> = {
        'center': 'items-center',
        'flex-start': 'items-start',
        'flex-end': 'items-end',
        'stretch': 'items-stretch',
      };
      if (alignMap[styles.alignItems]) {
        classes.push(alignMap[styles.alignItems]);
      }
    }

    // Add responsive classes if enabled
    if (this.options.responsive) {
      classes.push('w-full', 'max-w-full');
    }

    return classes.join(' ');
  }

  private generateComponentStyles(component: ParsedComponent): string {
    if (this.options.styling === 'styled-components') {
      return this.generateStyledComponents(component);
    } else if (this.options.styling === 'css' || this.options.styling === 'scss') {
      return this.generateCSSStyles(component);
    }
    return '';
  }

  private generateStyledComponents(component: ParsedComponent): string {
    const styles = Object.entries(component.styles)
      .map(([key, value]) => `  ${this.camelToKebab(key)}: ${value};`)
      .join('\n');

    return `const Styled${component.name} = styled.${component.type}\`\n${styles}\n\`;`;
  }

  private generateCSSStyles(component: ParsedComponent): string {
    const className = component.name.toLowerCase();
    const styles = Object.entries(component.styles)
      .map(([key, value]) => `  ${this.camelToKebab(key)}: ${value};`)
      .join('\n');

    return `.${className} {\n${styles}\n}`;
  }

  private camelToKebab(str: string): string {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
  }

  private shouldGenerateAsComponent(component: ParsedComponent): boolean {
    return component.children.length > 3 || 
           component.name.toLowerCase().includes('component') ||
           component.layout.isContainer && component.children.length > 0;
  }

  private generateVueComponent(component: ParsedComponent, isRoot: boolean): GeneratedFile {
    // Vue component generation logic would go here
    return {
      path: `src/components/${component.name}.vue`,
      content: `<!-- Vue component for ${component.name} -->`,
      type: isRoot ? 'page' : 'component',
    };
  }

  private generateAngularComponent(component: ParsedComponent, isRoot: boolean): GeneratedFile[] {
    // Angular component generation logic would go here
    return [{
      path: `src/app/components/${component.name.toLowerCase()}/${component.name.toLowerCase()}.component.ts`,
      content: `// Angular component for ${component.name}`,
      type: isRoot ? 'page' : 'component',
    }];
  }

  private generatePackageJson(): GeneratedFile {
    const packageJson = {
      name: this.options.projectName,
      version: '0.1.0',
      private: true,
      scripts: this.getScripts(),
      dependencies: this.getDependencies(),
      devDependencies: this.getDevDependencies(),
    };

    return {
      path: 'package.json',
      content: JSON.stringify(packageJson, null, 2),
      type: 'config',
    };
  }

  private getScripts(): Record<string, string> {
    switch (this.options.framework) {
      case 'react':
        return {
          start: 'react-scripts start',
          build: 'react-scripts build',
          test: 'react-scripts test',
          eject: 'react-scripts eject',
        };
      case 'vue':
        return {
          serve: 'vue-cli-service serve',
          build: 'vue-cli-service build',
          test: 'vue-cli-service test:unit',
        };
      case 'angular':
        return {
          start: 'ng serve',
          build: 'ng build',
          test: 'ng test',
        };
      default:
        return {};
    }
  }

  private getDependencies(): string[] {
    const deps = ['react', 'react-dom'];
    
    if (this.options.styling === 'styled-components') {
      deps.push('styled-components');
    } else if (this.options.styling === 'tailwind') {
      deps.push('tailwindcss');
    }

    return deps;
  }

  private getDevDependencies(): string[] {
    const devDeps = ['react-scripts'];
    
    if (this.options.typescript) {
      devDeps.push('@types/react', '@types/react-dom', 'typescript');
    }

    if (this.options.styling === 'styled-components' && this.options.typescript) {
      devDeps.push('@types/styled-components');
    }

    return devDeps;
  }

  private generateConfigFiles(): GeneratedFile[] {
    const files: GeneratedFile[] = [];

    if (this.options.typescript) {
      files.push({
        path: 'tsconfig.json',
        content: JSON.stringify({
          compilerOptions: {
            target: 'es5',
            lib: ['dom', 'dom.iterable', 'es6'],
            allowJs: true,
            skipLibCheck: true,
            esModuleInterop: true,
            allowSyntheticDefaultImports: true,
            strict: true,
            forceConsistentCasingInFileNames: true,
            moduleResolution: 'node',
            resolveJsonModule: true,
            isolatedModules: true,
            noEmit: true,
            jsx: 'react-jsx',
          },
          include: ['src'],
        }, null, 2),
        type: 'config',
      });
    }

    if (this.options.styling === 'tailwind') {
      files.push({
        path: 'tailwind.config.js',
        content: `module.exports = {
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  theme: {
    extend: {},
  },
  plugins: [],
};`,
        type: 'config',
      });
    }

    return files;
  }

  private generateGlobalStyles(): GeneratedFile {
    const ext = this.options.styling === 'scss' ? 'scss' : 'css';
    let content = '';

    if (this.options.styling === 'tailwind') {
      content = `@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}`;
    } else {
      content = `body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}`;
    }

    return {
      path: `src/index.${ext}`,
      content,
      type: 'style',
    };
  }

  private generateAppFile(): GeneratedFile {
    const ext = this.options.typescript ? 'tsx' : 'jsx';
    
    const content = `import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.${this.options.styling === 'scss' ? 'scss' : 'css'}';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`;

    return {
      path: `src/index.${ext}`,
      content,
      type: 'page',
    };
  }
}
