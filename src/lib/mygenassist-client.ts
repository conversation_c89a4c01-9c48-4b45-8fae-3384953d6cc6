import axios from 'axios';

export interface MyGenAssistResponse {
  success: boolean;
  data?: {
    content: string;
    usage?: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
    };
  };
  error?: string;
}

export interface CodeGenerationPrompt {
  componentData: unknown;
  framework: string;
  styling: string;
  typescript: boolean;
  responsive: boolean;
  accessibility: boolean;
}

export class MyGenAssistClient {
  private apiToken: string;
  private baseUrl: string;

  constructor(apiToken?: string) {
    this.apiToken = apiToken || process.env.MYGENASSIST_API_TOKEN || '';
    // Using a generic OpenAI-compatible endpoint - update this with your actual MyGenAssist API URL
    this.baseUrl = process.env.MYGENASSIST_API_URL || 'https://api.openai.com/v1';
  }

  async enhanceCodeGeneration(prompt: CodeGenerationPrompt): Promise<string | null> {
    if (!this.apiToken) {
      console.log('MyGenAssist token not provided, using basic code generation');
      return null;
    }

    try {
      const systemPrompt = this.buildSystemPrompt(prompt);
      const userPrompt = this.buildUserPrompt(prompt);

      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        {
          model: process.env.MYGENASSIST_MODEL || 'gpt-3.5-turbo', // Update with actual model name
          messages: [
            {
              role: 'system',
              content: systemPrompt,
            },
            {
              role: 'user',
              content: userPrompt,
            },
          ],
          max_tokens: 2000,
          temperature: 0.1,
          top_p: 0.9,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      // Handle both OpenAI format and custom format
      if (response.data?.choices?.[0]?.message?.content) {
        return response.data.choices[0].message.content;
      } else if (response.data?.success && response.data?.data?.content) {
        return response.data.data.content;
      }

      return null;
    } catch (error) {
      console.error('MyGenAssist API error:', error);
      return null;
    }
  }

  async optimizeComponent(componentCode: string, framework: string): Promise<string | null> {
    if (!this.apiToken) {
      return null;
    }

    try {
      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        {
          model: process.env.MYGENASSIST_MODEL || 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: `You are an expert ${framework} developer. Optimize the following component code for better performance, readability, and best practices. Return only the optimized code without explanations.`,
            },
            {
              role: 'user',
              content: componentCode,
            },
          ],
          max_tokens: 1500,
          temperature: 0.1,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      // Handle both OpenAI format and custom format
      if (response.data?.choices?.[0]?.message?.content) {
        return response.data.choices[0].message.content;
      } else if (response.data?.success && response.data?.data?.content) {
        return response.data.data.content;
      }

      return null;
    } catch (error) {
      console.error('MyGenAssist optimization error:', error);
      return null;
    }
  }

  async generateAccessibilityAttributes(componentType: string, componentName: string): Promise<Record<string, string> | null> {
    if (!this.apiToken) {
      return null;
    }

    try {
      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        {
          model: process.env.MYGENASSIST_MODEL || 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'You are an accessibility expert. Generate appropriate ARIA attributes and accessibility properties for the given component. Return only a JSON object with the attributes.',
            },
            {
              role: 'user',
              content: `Component type: ${componentType}, Component name: ${componentName}`,
            },
          ],
          max_tokens: 500,
          temperature: 0.1,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      let content = null;
      // Handle both OpenAI format and custom format
      if (response.data?.choices?.[0]?.message?.content) {
        content = response.data.choices[0].message.content;
      } else if (response.data?.success && response.data?.data?.content) {
        content = response.data.data.content;
      }

      if (content) {
        try {
          return JSON.parse(content);
        } catch {
          return null;
        }
      }

      return null;
    } catch (error) {
      console.error('MyGenAssist accessibility error:', error);
      return null;
    }
  }

  private buildSystemPrompt(prompt: CodeGenerationPrompt): string {
    return `You are an expert frontend developer specializing in ${prompt.framework} development. 
Your task is to generate high-quality, production-ready code based on Figma design data.

Requirements:
- Framework: ${prompt.framework}
- Styling: ${prompt.styling}
- TypeScript: ${prompt.typescript ? 'Yes' : 'No'}
- Responsive: ${prompt.responsive ? 'Yes' : 'No'}
- Accessibility: ${prompt.accessibility ? 'Yes' : 'No'}

Generate clean, maintainable code that follows best practices for the specified framework and styling approach.
${prompt.accessibility ? 'Include proper ARIA attributes and semantic HTML for accessibility.' : ''}
${prompt.responsive ? 'Ensure the code is responsive and mobile-friendly.' : ''}
${prompt.typescript ? 'Use proper TypeScript types and interfaces.' : ''}

Return only the code without explanations or markdown formatting.`;
  }

  private buildUserPrompt(prompt: CodeGenerationPrompt): string {
    return `Generate a ${prompt.framework} component based on this design data:

${JSON.stringify(prompt.componentData, null, 2)}

The component should be styled using ${prompt.styling} and follow the design specifications exactly.`;
  }

  async testConnection(): Promise<boolean> {
    if (!this.apiToken) {
      return false;
    }

    try {
      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        {
          model: process.env.MYGENASSIST_MODEL || 'gpt-3.5-turbo',
          messages: [
            {
              role: 'user',
              content: 'Hello, this is a test message.',
            },
          ],
          max_tokens: 10,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      // Handle both OpenAI format and custom format
      return response.data?.choices?.[0]?.message?.content || response.data?.success === true;
    } catch (error) {
      console.error('MyGenAssist connection test failed:', error);
      return false;
    }
  }
}
