import axios from 'axios';

export interface FigmaFile {
  document: FigmaNode;
  components: Record<string, FigmaComponent>;
  styles: Record<string, FigmaStyle>;
  name: string;
  lastModified: string;
  thumbnailUrl: string;
  version: string;
}

export interface FigmaNode {
  id: string;
  name: string;
  type: string;
  children?: FigmaNode[];
  backgroundColor?: FigmaColor;
  fills?: FigmaFill[];
  strokes?: FigmaStroke[];
  strokeWeight?: number;
  cornerRadius?: number;
  constraints?: FigmaConstraints;
  layoutMode?: string;
  primaryAxisSizingMode?: string;
  counterAxisSizingMode?: string;
  paddingLeft?: number;
  paddingRight?: number;
  paddingTop?: number;
  paddingBottom?: number;
  itemSpacing?: number;
  layoutGrow?: number;
  layoutAlign?: string;
  absoluteBoundingBox?: FigmaBoundingBox;
  size?: FigmaSize;
  relativeTransform?: number[][];
  characters?: string;
  style?: FigmaTextStyle;
  characterStyleOverrides?: number[];
  styleOverrideTable?: Record<string, FigmaTextStyle>;
  opacity?: number;
  effects?: FigmaEffect[];
  primaryAxisAlignItems?: string;
  counterAxisAlignItems?: string;
}

export interface FigmaComponent {
  key: string;
  name: string;
  description: string;
  componentSetId?: string;
  documentationLinks: unknown[];
}

export interface FigmaStyle {
  key: string;
  name: string;
  description: string;
  styleType: string;
}

export interface FigmaColor {
  r: number;
  g: number;
  b: number;
  a: number;
}

export interface FigmaFill {
  type: string;
  color?: FigmaColor;
  gradientStops?: FigmaGradientStop[];
  gradientTransform?: number[][];
}

export interface FigmaStroke {
  type: string;
  color?: FigmaColor;
}

export interface FigmaGradientStop {
  position: number;
  color: FigmaColor;
}

export interface FigmaConstraints {
  vertical: string;
  horizontal: string;
}

export interface FigmaBoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface FigmaSize {
  x: number;
  y: number;
}

export interface FigmaTextStyle {
  fontFamily: string;
  fontPostScriptName?: string;
  fontWeight: number;
  fontSize: number;
  lineHeightPx?: number;
  lineHeightPercent?: number;
  letterSpacing?: number;
  textAlignHorizontal?: string;
  textAlignVertical?: string;
  textDecoration?: string;
  fills?: FigmaFill[];
}

export interface FigmaEffect {
  type: string;
  visible?: boolean;
  radius?: number;
  color?: FigmaColor;
  offset?: {
    x: number;
    y: number;
  };
  spread?: number;
}

export class FigmaClient {
  private accessToken: string;
  private baseUrl = 'https://api.figma.com/v1';

  constructor(accessToken: string) {
    this.accessToken = accessToken;
  }

  private async makeRequest(endpoint: string) {
    try {
      const response = await axios.get(`${this.baseUrl}${endpoint}`, {
        headers: {
          'X-Figma-Token': this.accessToken,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Figma API request failed:', error);
      throw new Error(`Failed to fetch from Figma API: ${error}`);
    }
  }

  async getFile(fileKey: string): Promise<FigmaFile> {
    return this.makeRequest(`/files/${fileKey}`);
  }

  async getFileNodes(fileKey: string, nodeIds: string[]) {
    const nodeIdsParam = nodeIds.join(',');
    return this.makeRequest(`/files/${fileKey}/nodes?ids=${nodeIdsParam}`);
  }

  async getImages(fileKey: string, nodeIds: string[], format: string = 'png', scale: number = 1) {
    const nodeIdsParam = nodeIds.join(',');
    return this.makeRequest(`/images/${fileKey}?ids=${nodeIdsParam}&format=${format}&scale=${scale}`);
  }

  async downloadImage(imageUrl: string): Promise<Buffer> {
    try {
      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
      });
      return Buffer.from(response.data);
    } catch (error) {
      console.error('Failed to download image:', error);
      throw new Error(`Failed to download image from ${imageUrl}`);
    }
  }

  extractImageNodes(node: FigmaNode, imageNodes: FigmaNode[] = []): FigmaNode[] {
    // Check if current node has image fills
    if (this.hasImageFill(node)) {
      imageNodes.push(node);
    }

    // Recursively check children
    if (node.children) {
      for (const child of node.children) {
        this.extractImageNodes(child, imageNodes);
      }
    }

    return imageNodes;
  }

  private hasImageFill(node: FigmaNode): boolean {
    if (node.fills) {
      return node.fills.some(fill => fill.type === 'IMAGE');
    }
    return false;
  }

  async getComments(fileKey: string) {
    return this.makeRequest(`/files/${fileKey}/comments`);
  }

  async getTeamProjects(teamId: string) {
    return this.makeRequest(`/teams/${teamId}/projects`);
  }

  async getProjectFiles(projectId: string) {
    return this.makeRequest(`/projects/${projectId}/files`);
  }

  // Helper method to extract file key from Figma URL
  static extractFileKey(figmaUrl: string): string | null {
    // Handle regular file URLs: /file/FILE_KEY/
    let match = figmaUrl.match(/\/file\/([a-zA-Z0-9]+)/);
    if (match) {
      return match[1];
    }

    // Handle community file URLs: /community/file/FILE_KEY/
    match = figmaUrl.match(/\/community\/file\/([a-zA-Z0-9]+)/);
    if (match) {
      return match[1];
    }

    return null;
  }

  // Helper method to extract node ID from Figma URL
  static extractNodeId(figmaUrl: string): string | null {
    const match = figmaUrl.match(/node-id=([^&]+)/);
    return match ? decodeURIComponent(match[1]) : null;
  }
}
