import { FigmaNode, FigmaColor } from './figma-client';

export interface ParsedComponent {
  id: string;
  name: string;
  type: ComponentType;
  props: ComponentProps;
  children: ParsedComponent[];
  styles: ComponentStyles;
  layout: LayoutInfo;
}

export interface ComponentProps {
  text?: string;
  src?: string;
  alt?: string;
  href?: string;
  placeholder?: string;
  type?: string;
  value?: string;
  onClick?: string;
  className?: string;
  [key: string]: string | number | boolean | undefined;
}

export interface ComponentStyles {
  width?: string;
  height?: string;
  backgroundColor?: string;
  color?: string;
  fontSize?: string;
  fontFamily?: string;
  fontWeight?: string;
  lineHeight?: string;
  letterSpacing?: string;
  textAlign?: string;
  padding?: string;
  margin?: string;
  border?: string;
  borderRadius?: string;
  display?: string;
  flexDirection?: string;
  justifyContent?: string;
  alignItems?: string;
  gap?: string;
  position?: string;
  top?: string;
  left?: string;
  right?: string;
  bottom?: string;
  zIndex?: string;
  overflow?: string;
  boxShadow?: string;
  [key: string]: string | undefined;
}

export interface LayoutInfo {
  isContainer: boolean;
  layoutMode?: 'HORIZONTAL' | 'VERTICAL' | 'NONE';
  primaryAxisSizingMode?: string;
  counterAxisSizingMode?: string;
  paddingLeft?: number;
  paddingRight?: number;
  paddingTop?: number;
  paddingBottom?: number;
  itemSpacing?: number;
  layoutGrow?: number;
  layoutAlign?: string;
}

export enum ComponentType {
  CONTAINER = 'div',
  TEXT = 'span',
  HEADING = 'h1',
  BUTTON = 'button',
  INPUT = 'input',
  IMAGE = 'img',
  LINK = 'a',
  LIST = 'ul',
  LIST_ITEM = 'li',
  SECTION = 'section',
  HEADER = 'header',
  FOOTER = 'footer',
  NAV = 'nav',
  MAIN = 'main',
  ASIDE = 'aside',
  ARTICLE = 'article',
}

export class DesignParser {
  private componentCounter = 0;

  parse(figmaNode: FigmaNode): ParsedComponent {
    return this.parseNode(figmaNode);
  }

  private parseNode(node: FigmaNode): ParsedComponent {
    const componentType = this.determineComponentType(node);
    const styles = this.extractStyles(node);
    const layout = this.extractLayout(node);
    const props = this.extractProps(node, componentType);

    const children = node.children 
      ? node.children.map(child => this.parseNode(child))
      : [];

    return {
      id: this.generateComponentId(node.name),
      name: this.sanitizeComponentName(node.name),
      type: componentType,
      props,
      children,
      styles,
      layout,
    };
  }

  private determineComponentType(node: FigmaNode): ComponentType {
    const nodeName = node.name.toLowerCase();
    const nodeType = node.type;

    // Check by name patterns first (more comprehensive patterns)
    if (this.matchesPattern(nodeName, ['button', 'btn', 'cta', 'submit', 'action'])) {
      return ComponentType.BUTTON;
    }
    if (this.matchesPattern(nodeName, ['input', 'field', 'textbox', 'form', 'search', 'email', 'password'])) {
      return ComponentType.INPUT;
    }
    if (this.matchesPattern(nodeName, ['image', 'img', 'photo', 'picture', 'avatar', 'icon', 'logo'])) {
      return ComponentType.IMAGE;
    }
    if (this.matchesPattern(nodeName, ['link', 'anchor', 'url', 'href'])) {
      return ComponentType.LINK;
    }
    if (this.matchesPattern(nodeName, ['nav', 'menu', 'navigation', 'navbar', 'sidebar'])) {
      return ComponentType.NAV;
    }
    if (this.matchesPattern(nodeName, ['header', 'top', 'banner'])) {
      return ComponentType.HEADER;
    }
    if (this.matchesPattern(nodeName, ['footer', 'bottom'])) {
      return ComponentType.FOOTER;
    }
    if (this.matchesPattern(nodeName, ['main', 'content', 'body'])) {
      return ComponentType.MAIN;
    }
    if (this.matchesPattern(nodeName, ['aside', 'sidebar'])) {
      return ComponentType.ASIDE;
    }
    if (this.matchesPattern(nodeName, ['article', 'post', 'blog'])) {
      return ComponentType.ARTICLE;
    }
    if (this.matchesPattern(nodeName, ['section', 'block', 'panel', 'card'])) {
      return ComponentType.SECTION;
    }
    if (this.matchesPattern(nodeName, ['heading', 'title', 'header']) || /^h[1-6]/.test(nodeName)) {
      return ComponentType.HEADING;
    }
    if (this.matchesPattern(nodeName, ['list', 'menu', 'items'])) {
      return ComponentType.LIST;
    }
    if (this.matchesPattern(nodeName, ['item', 'entry']) && (!node.children || node.children.length === 0)) {
      return ComponentType.LIST_ITEM;
    }

    // Enhanced type detection based on Figma node properties
    switch (nodeType) {
      case 'TEXT':
        return this.isHeading(node) ? ComponentType.HEADING : ComponentType.TEXT;
      case 'RECTANGLE':
        // Check if it's likely a button based on size and styling
        if (this.looksLikeButton(node)) {
          return ComponentType.BUTTON;
        }
        return this.hasImageFill(node) ? ComponentType.IMAGE : ComponentType.CONTAINER;
      case 'ELLIPSE':
      case 'POLYGON':
      case 'STAR':
      case 'VECTOR':
        return this.hasImageFill(node) ? ComponentType.IMAGE : ComponentType.CONTAINER;
      case 'FRAME':
      case 'GROUP':
        // Analyze frame content to determine if it's a specific component
        return this.analyzeFrameContent(node);
      case 'COMPONENT':
      case 'INSTANCE':
        return this.analyzeComponentInstance(node);
      default:
        return ComponentType.CONTAINER;
    }
  }

  private matchesPattern(name: string, patterns: string[]): boolean {
    return patterns.some(pattern => name.includes(pattern));
  }

  private looksLikeButton(node: FigmaNode): boolean {
    if (!node.absoluteBoundingBox) return false;

    const { width, height } = node.absoluteBoundingBox;
    const aspectRatio = width / height;

    // Typical button characteristics
    const hasReasonableSize = width > 60 && width < 400 && height > 20 && height < 80;
    const hasButtonAspectRatio = aspectRatio > 1.5 && aspectRatio < 8;
    const hasRoundedCorners = (node.cornerRadius || 0) > 0;
    const hasBackground = !!(node.fills && node.fills.length > 0);

    return hasReasonableSize && hasButtonAspectRatio && (hasRoundedCorners || hasBackground);
  }

  private analyzeFrameContent(node: FigmaNode): ComponentType {
    if (!node.children || node.children.length === 0) {
      return ComponentType.CONTAINER;
    }

    // Check if it's a card-like structure
    if (this.looksLikeCard(node)) {
      return ComponentType.SECTION;
    }

    // Check if it's a navigation structure
    if (this.looksLikeNavigation(node)) {
      return ComponentType.NAV;
    }

    // Check if it's a form structure
    if (this.looksLikeForm(node)) {
      return ComponentType.SECTION;
    }

    return ComponentType.CONTAINER;
  }

  private analyzeComponentInstance(node: FigmaNode): ComponentType {
    // For component instances, try to infer type from the component name
    const componentName = node.name.toLowerCase();

    if (this.matchesPattern(componentName, ['button', 'btn'])) {
      return ComponentType.BUTTON;
    }
    if (this.matchesPattern(componentName, ['input', 'field'])) {
      return ComponentType.INPUT;
    }
    if (this.matchesPattern(componentName, ['card', 'item'])) {
      return ComponentType.SECTION;
    }

    return ComponentType.CONTAINER;
  }

  private looksLikeCard(node: FigmaNode): boolean {
    if (!node.children) return false;

    const hasBackground = !!(node.fills && node.fills.length > 0) || !!(node.backgroundColor);
    const hasRoundedCorners = (node.cornerRadius || 0) > 0;
    const hasPadding = !!(node.paddingLeft || node.paddingRight || node.paddingTop || node.paddingBottom);
    const hasMultipleChildren = node.children.length > 1;

    return hasBackground && (hasRoundedCorners || hasPadding) && hasMultipleChildren;
  }

  private looksLikeNavigation(node: FigmaNode): boolean {
    if (!node.children) return false;

    const hasHorizontalLayout = node.layoutMode === 'HORIZONTAL';
    const hasMultipleItems = node.children.length > 2;
    const childrenLookLikeNavItems = node.children.some(child =>
      child.type === 'TEXT' || this.matchesPattern(child.name.toLowerCase(), ['link', 'item', 'tab'])
    );

    return hasHorizontalLayout && hasMultipleItems && childrenLookLikeNavItems;
  }

  private looksLikeForm(node: FigmaNode): boolean {
    if (!node.children) return false;

    const hasInputLikeChildren = node.children.some(child =>
      this.matchesPattern(child.name.toLowerCase(), ['input', 'field', 'textbox', 'button', 'submit'])
    );
    const hasVerticalLayout = node.layoutMode === 'VERTICAL';

    return hasInputLikeChildren && hasVerticalLayout;
  }

  private isHeading(node: FigmaNode): boolean {
    if (!node.style) return false;
    const fontSize = node.style.fontSize || 16;
    return fontSize > 20; // Consider text larger than 20px as heading
  }

  private hasImageFill(node: FigmaNode): boolean {
    if (!node.fills) return false;
    return node.fills.some(fill => fill.type === 'IMAGE');
  }

  private extractStyles(node: FigmaNode): ComponentStyles {
    const styles: ComponentStyles = {};

    // Dimensions - make responsive by default
    if (node.absoluteBoundingBox) {
      const { width, height } = node.absoluteBoundingBox;

      // Use relative units for better responsiveness
      if (width < 100) {
        styles.width = `${width}px`;
      } else if (width > 1200) {
        styles.width = '100%';
        styles.maxWidth = `${width}px`;
      } else {
        styles.width = `${width}px`;
      }

      // Height handling
      if (height > 0) {
        styles.height = `${height}px`;
      }
    }

    // Background handling with gradients and images
    this.extractBackgroundStyles(node, styles);

    // Border and corner radius
    this.extractBorderStyles(node, styles);

    // Text styles with better font handling
    this.extractTextStyles(node, styles);

    // Layout styles
    this.extractLayoutStyles(node, styles);

    // Shadow effects
    this.extractShadowStyles(node, styles);

    // Opacity
    if (node.opacity !== undefined && node.opacity < 1) {
      styles.opacity = node.opacity.toString();
    }

    return styles;
  }

  private extractBackgroundStyles(node: FigmaNode, styles: ComponentStyles): void {
    // Background color
    if (node.backgroundColor) {
      styles.backgroundColor = this.colorToCSS(node.backgroundColor);
    }

    // Fills (can include gradients, images, etc.)
    if (node.fills && node.fills.length > 0) {
      const primaryFill = node.fills[0];

      switch (primaryFill.type) {
        case 'SOLID':
          if (primaryFill.color) {
            styles.backgroundColor = this.colorToCSS(primaryFill.color);
          }
          break;
        case 'GRADIENT_LINEAR':
          styles.background = this.gradientToCSS(primaryFill);
          break;
        case 'GRADIENT_RADIAL':
          styles.background = this.radialGradientToCSS(primaryFill);
          break;
        case 'IMAGE':
          styles.backgroundImage = 'url(/placeholder-image.jpg)';
          styles.backgroundSize = 'cover';
          styles.backgroundPosition = 'center';
          break;
      }
    }
  }

  private extractBorderStyles(node: FigmaNode, styles: ComponentStyles): void {
    // Corner radius
    if (node.cornerRadius) {
      styles.borderRadius = `${node.cornerRadius}px`;
    }

    // Strokes (borders)
    if (node.strokes && node.strokes.length > 0 && node.strokeWeight) {
      const stroke = node.strokes[0];
      if (stroke.type === 'SOLID' && stroke.color) {
        styles.border = `${node.strokeWeight}px solid ${this.colorToCSS(stroke.color)}`;
      }
    }
  }

  private extractTextStyles(node: FigmaNode, styles: ComponentStyles): void {
    if (!node.style) return;

    const textStyle = node.style;

    if (textStyle.fontSize) {
      styles.fontSize = `${textStyle.fontSize}px`;
    }

    if (textStyle.fontFamily) {
      styles.fontFamily = this.normalizeFontFamily(textStyle.fontFamily);
    }

    if (textStyle.fontWeight) {
      styles.fontWeight = textStyle.fontWeight.toString();
    }

    if (textStyle.lineHeightPx) {
      styles.lineHeight = `${textStyle.lineHeightPx}px`;
    } else if (textStyle.lineHeightPercent) {
      styles.lineHeight = `${textStyle.lineHeightPercent / 100}`;
    }

    if (textStyle.letterSpacing) {
      styles.letterSpacing = `${textStyle.letterSpacing}px`;
    }

    if (textStyle.textAlignHorizontal) {
      styles.textAlign = textStyle.textAlignHorizontal.toLowerCase();
    }

    // Text decoration
    if (textStyle.textDecoration) {
      styles.textDecoration = textStyle.textDecoration.toLowerCase();
    }

    // Text color from fills
    if (textStyle.fills && textStyle.fills.length > 0) {
      const textFill = textStyle.fills[0];
      if (textFill.type === 'SOLID' && textFill.color) {
        styles.color = this.colorToCSS(textFill.color);
      }
    }
  }

  private extractLayoutStyles(node: FigmaNode, styles: ComponentStyles): void {

    // Layout mode (Auto Layout)
    if (node.layoutMode) {
      styles.display = 'flex';
      styles.flexDirection = node.layoutMode === 'HORIZONTAL' ? 'row' : 'column';

      // Alignment properties
      if (node.primaryAxisAlignItems) {
        const alignMap: Record<string, string> = {
          'MIN': 'flex-start',
          'CENTER': 'center',
          'MAX': 'flex-end',
          'SPACE_BETWEEN': 'space-between',
        };
        styles.justifyContent = alignMap[node.primaryAxisAlignItems] || 'flex-start';
      }

      if (node.counterAxisAlignItems) {
        const alignMap: Record<string, string> = {
          'MIN': 'flex-start',
          'CENTER': 'center',
          'MAX': 'flex-end',
        };
        styles.alignItems = alignMap[node.counterAxisAlignItems] || 'flex-start';
      }
    }

    // Padding
    if (node.paddingLeft || node.paddingRight || node.paddingTop || node.paddingBottom) {
      const top = node.paddingTop || 0;
      const right = node.paddingRight || 0;
      const bottom = node.paddingBottom || 0;
      const left = node.paddingLeft || 0;

      if (top === right && right === bottom && bottom === left) {
        styles.padding = `${top}px`;
      } else if (top === bottom && left === right) {
        styles.padding = `${top}px ${right}px`;
      } else {
        styles.padding = `${top}px ${right}px ${bottom}px ${left}px`;
      }
    }

    // Gap (spacing between items)
    if (node.itemSpacing) {
      styles.gap = `${node.itemSpacing}px`;
    }

    // Position and constraints
    if (node.constraints) {
      this.applyConstraints(node, styles);
    }
  }

  private extractShadowStyles(node: FigmaNode, styles: ComponentStyles): void {
    if (node.effects && node.effects.length > 0) {
      const shadows = node.effects
        .filter(effect => effect.type === 'DROP_SHADOW' && effect.visible !== false)
        .map(effect => this.shadowToCSS(effect));

      if (shadows.length > 0) {
        styles.boxShadow = shadows.join(', ');
      }
    }
  }

  private gradientToCSS(fill: any): string {
    if (!fill.gradientStops || fill.gradientStops.length < 2) {
      return 'transparent';
    }

    const stops = fill.gradientStops
      .map((stop: any) => `${this.colorToCSS(stop.color)} ${Math.round(stop.position * 100)}%`)
      .join(', ');

    // Calculate angle from gradient transform
    const angle = this.calculateGradientAngle(fill.gradientTransform);

    return `linear-gradient(${angle}deg, ${stops})`;
  }

  private radialGradientToCSS(fill: any): string {
    if (!fill.gradientStops || fill.gradientStops.length < 2) {
      return 'transparent';
    }

    const stops = fill.gradientStops
      .map((stop: any) => `${this.colorToCSS(stop.color)} ${Math.round(stop.position * 100)}%`)
      .join(', ');

    return `radial-gradient(circle, ${stops})`;
  }

  private shadowToCSS(effect: any): string {
    const x = effect.offset?.x || 0;
    const y = effect.offset?.y || 0;
    const blur = effect.radius || 0;
    const spread = effect.spread || 0;
    const color = effect.color ? this.colorToCSS(effect.color) : 'rgba(0, 0, 0, 0.25)';

    return `${x}px ${y}px ${blur}px ${spread}px ${color}`;
  }

  private calculateGradientAngle(transform: number[][]): number {
    if (!transform || transform.length < 2) return 0;

    // Extract angle from transformation matrix
    const dx = transform[0][0];
    const dy = transform[1][0];
    const angle = Math.atan2(dy, dx) * (180 / Math.PI);

    return Math.round(angle);
  }

  private normalizeFontFamily(fontFamily: string): string {
    // Map Figma font names to web-safe fonts
    const fontMap: Record<string, string> = {
      'Inter': '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      'Roboto': '"Roboto", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
      'Open Sans': '"Open Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
      'Helvetica': '"Helvetica Neue", Helvetica, Arial, sans-serif',
      'Arial': 'Arial, sans-serif',
      'Times': '"Times New Roman", Times, serif',
      'Georgia': 'Georgia, serif',
    };

    return fontMap[fontFamily] || `"${fontFamily}", sans-serif`;
  }

  private applyConstraints(node: FigmaNode, styles: ComponentStyles): void {
    if (!node.constraints) return;

    const { horizontal, vertical } = node.constraints;

    // Handle horizontal constraints
    if (horizontal === 'STRETCH') {
      styles.width = '100%';
    } else if (horizontal === 'RIGHT') {
      styles.marginLeft = 'auto';
    } else if (horizontal === 'CENTER') {
      styles.margin = '0 auto';
    }

    // Handle vertical constraints
    if (vertical === 'STRETCH') {
      styles.height = '100%';
    } else if (vertical === 'BOTTOM') {
      styles.marginTop = 'auto';
    } else if (vertical === 'CENTER') {
      styles.alignSelf = 'center';
    }
  }

  private extractLayout(node: FigmaNode): LayoutInfo {
    return {
      isContainer: !!(node.children && node.children.length > 0),
      layoutMode: node.layoutMode as 'HORIZONTAL' | 'VERTICAL' | 'NONE',
      primaryAxisSizingMode: node.primaryAxisSizingMode,
      counterAxisSizingMode: node.counterAxisSizingMode,
      paddingLeft: node.paddingLeft,
      paddingRight: node.paddingRight,
      paddingTop: node.paddingTop,
      paddingBottom: node.paddingBottom,
      itemSpacing: node.itemSpacing,
      layoutGrow: node.layoutGrow,
      layoutAlign: node.layoutAlign,
    };
  }

  private extractProps(node: FigmaNode, componentType: ComponentType): ComponentProps {
    const props: ComponentProps = {};

    switch (componentType) {
      case ComponentType.TEXT:
      case ComponentType.HEADING:
        if (node.characters) {
          props.text = node.characters;
        }
        break;
      case ComponentType.BUTTON:
        if (node.characters) {
          props.text = node.characters;
        }
        props.onClick = 'handleClick';
        break;
      case ComponentType.INPUT:
        props.type = 'text';
        props.placeholder = node.name || 'Enter text';
        break;
      case ComponentType.IMAGE:
        props.src = '/placeholder-image.jpg';
        props.alt = node.name || 'Image';
        break;
      case ComponentType.LINK:
        props.href = '#';
        if (node.characters) {
          props.text = node.characters;
        }
        break;
    }

    return props;
  }

  private colorToCSS(color: FigmaColor): string {
    const r = Math.round(color.r * 255);
    const g = Math.round(color.g * 255);
    const b = Math.round(color.b * 255);
    const a = color.a;

    if (a === 1) {
      return `rgb(${r}, ${g}, ${b})`;
    } else {
      return `rgba(${r}, ${g}, ${b}, ${a})`;
    }
  }

  private generateComponentId(name: string): string {
    const sanitized = this.sanitizeComponentName(name);
    return `${sanitized}_${++this.componentCounter}`;
  }

  private sanitizeComponentName(name: string): string {
    return name
      .replace(/[^a-zA-Z0-9]/g, '_')
      .replace(/^[0-9]/, '_$&')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')
      || 'Component';
  }
}
