import { FigmaNode, FigmaColor } from './figma-client';

export interface ParsedComponent {
  id: string;
  name: string;
  type: ComponentType;
  props: ComponentProps;
  children: ParsedComponent[];
  styles: ComponentStyles;
  layout: LayoutInfo;
}

export interface ComponentProps {
  text?: string;
  src?: string;
  alt?: string;
  href?: string;
  placeholder?: string;
  type?: string;
  value?: string;
  onClick?: string;
  className?: string;
  [key: string]: string | number | boolean | undefined;
}

export interface ComponentStyles {
  width?: string;
  height?: string;
  backgroundColor?: string;
  color?: string;
  fontSize?: string;
  fontFamily?: string;
  fontWeight?: string;
  lineHeight?: string;
  letterSpacing?: string;
  textAlign?: string;
  padding?: string;
  margin?: string;
  border?: string;
  borderRadius?: string;
  display?: string;
  flexDirection?: string;
  justifyContent?: string;
  alignItems?: string;
  gap?: string;
  position?: string;
  top?: string;
  left?: string;
  right?: string;
  bottom?: string;
  zIndex?: string;
  overflow?: string;
  boxShadow?: string;
  [key: string]: string | undefined;
}

export interface LayoutInfo {
  isContainer: boolean;
  layoutMode?: 'HORIZONTAL' | 'VERTICAL' | 'NONE';
  primaryAxisSizingMode?: string;
  counterAxisSizingMode?: string;
  paddingLeft?: number;
  paddingRight?: number;
  paddingTop?: number;
  paddingBottom?: number;
  itemSpacing?: number;
  layoutGrow?: number;
  layoutAlign?: string;
}

export enum ComponentType {
  CONTAINER = 'div',
  TEXT = 'span',
  HEADING = 'h1',
  BUTTON = 'button',
  INPUT = 'input',
  IMAGE = 'img',
  LINK = 'a',
  LIST = 'ul',
  LIST_ITEM = 'li',
  SECTION = 'section',
  HEADER = 'header',
  FOOTER = 'footer',
  NAV = 'nav',
  MAIN = 'main',
  ASIDE = 'aside',
  ARTICLE = 'article',
}

export class DesignParser {
  private componentCounter = 0;

  parse(figmaNode: FigmaNode): ParsedComponent {
    return this.parseNode(figmaNode);
  }

  private parseNode(node: FigmaNode): ParsedComponent {
    const componentType = this.determineComponentType(node);
    const styles = this.extractStyles(node);
    const layout = this.extractLayout(node);
    const props = this.extractProps(node, componentType);

    const children = node.children 
      ? node.children.map(child => this.parseNode(child))
      : [];

    return {
      id: this.generateComponentId(node.name),
      name: this.sanitizeComponentName(node.name),
      type: componentType,
      props,
      children,
      styles,
      layout,
    };
  }

  private determineComponentType(node: FigmaNode): ComponentType {
    const nodeName = node.name.toLowerCase();
    const nodeType = node.type;

    // Check for specific component patterns in name
    if (nodeName.includes('button') || nodeName.includes('btn')) {
      return ComponentType.BUTTON;
    }
    if (nodeName.includes('input') || nodeName.includes('textfield')) {
      return ComponentType.INPUT;
    }
    if (nodeName.includes('image') || nodeName.includes('img') || nodeName.includes('photo')) {
      return ComponentType.IMAGE;
    }
    if (nodeName.includes('link') || nodeName.includes('anchor')) {
      return ComponentType.LINK;
    }
    if (nodeName.includes('header')) {
      return ComponentType.HEADER;
    }
    if (nodeName.includes('footer')) {
      return ComponentType.FOOTER;
    }
    if (nodeName.includes('nav')) {
      return ComponentType.NAV;
    }
    if (nodeName.includes('main')) {
      return ComponentType.MAIN;
    }
    if (nodeName.includes('aside') || nodeName.includes('sidebar')) {
      return ComponentType.ASIDE;
    }
    if (nodeName.includes('article')) {
      return ComponentType.ARTICLE;
    }
    if (nodeName.includes('section')) {
      return ComponentType.SECTION;
    }
    if (nodeName.includes('heading') || nodeName.includes('title') || /^h[1-6]/.test(nodeName)) {
      return ComponentType.HEADING;
    }
    if (nodeName.includes('list')) {
      return ComponentType.LIST;
    }
    if (nodeName.includes('item') && node.children && node.children.length === 0) {
      return ComponentType.LIST_ITEM;
    }

    // Check by Figma node type
    switch (nodeType) {
      case 'TEXT':
        return this.isHeading(node) ? ComponentType.HEADING : ComponentType.TEXT;
      case 'RECTANGLE':
      case 'ELLIPSE':
      case 'POLYGON':
      case 'STAR':
      case 'VECTOR':
        return this.hasImageFill(node) ? ComponentType.IMAGE : ComponentType.CONTAINER;
      case 'FRAME':
      case 'GROUP':
      case 'COMPONENT':
      case 'INSTANCE':
        return ComponentType.CONTAINER;
      default:
        return ComponentType.CONTAINER;
    }
  }

  private isHeading(node: FigmaNode): boolean {
    if (!node.style) return false;
    const fontSize = node.style.fontSize || 16;
    return fontSize > 20; // Consider text larger than 20px as heading
  }

  private hasImageFill(node: FigmaNode): boolean {
    if (!node.fills) return false;
    return node.fills.some(fill => fill.type === 'IMAGE');
  }

  private extractStyles(node: FigmaNode): ComponentStyles {
    const styles: ComponentStyles = {};

    // Dimensions
    if (node.absoluteBoundingBox) {
      styles.width = `${node.absoluteBoundingBox.width}px`;
      styles.height = `${node.absoluteBoundingBox.height}px`;
    }

    // Background color
    if (node.backgroundColor) {
      styles.backgroundColor = this.colorToCSS(node.backgroundColor);
    }

    // Fills
    if (node.fills && node.fills.length > 0) {
      const primaryFill = node.fills[0];
      if (primaryFill.type === 'SOLID' && primaryFill.color) {
        styles.backgroundColor = this.colorToCSS(primaryFill.color);
      }
    }

    // Border radius
    if (node.cornerRadius) {
      styles.borderRadius = `${node.cornerRadius}px`;
    }

    // Text styles
    if (node.style) {
      styles.fontSize = `${node.style.fontSize}px`;
      styles.fontFamily = node.style.fontFamily;
      styles.fontWeight = node.style.fontWeight.toString();
      
      if (node.style.lineHeightPx) {
        styles.lineHeight = `${node.style.lineHeightPx}px`;
      }
      
      if (node.style.letterSpacing) {
        styles.letterSpacing = `${node.style.letterSpacing}px`;
      }
      
      if (node.style.textAlignHorizontal) {
        styles.textAlign = node.style.textAlignHorizontal.toLowerCase();
      }

      // Text color from fills
      if (node.style.fills && node.style.fills.length > 0) {
        const textFill = node.style.fills[0];
        if (textFill.type === 'SOLID' && textFill.color) {
          styles.color = this.colorToCSS(textFill.color);
        }
      }
    }

    // Layout styles
    if (node.layoutMode) {
      styles.display = 'flex';
      styles.flexDirection = node.layoutMode === 'HORIZONTAL' ? 'row' : 'column';
    }

    // Padding
    if (node.paddingLeft || node.paddingRight || node.paddingTop || node.paddingBottom) {
      const top = node.paddingTop || 0;
      const right = node.paddingRight || 0;
      const bottom = node.paddingBottom || 0;
      const left = node.paddingLeft || 0;
      styles.padding = `${top}px ${right}px ${bottom}px ${left}px`;
    }

    // Gap
    if (node.itemSpacing) {
      styles.gap = `${node.itemSpacing}px`;
    }

    return styles;
  }

  private extractLayout(node: FigmaNode): LayoutInfo {
    return {
      isContainer: !!(node.children && node.children.length > 0),
      layoutMode: node.layoutMode as 'HORIZONTAL' | 'VERTICAL' | 'NONE',
      primaryAxisSizingMode: node.primaryAxisSizingMode,
      counterAxisSizingMode: node.counterAxisSizingMode,
      paddingLeft: node.paddingLeft,
      paddingRight: node.paddingRight,
      paddingTop: node.paddingTop,
      paddingBottom: node.paddingBottom,
      itemSpacing: node.itemSpacing,
      layoutGrow: node.layoutGrow,
      layoutAlign: node.layoutAlign,
    };
  }

  private extractProps(node: FigmaNode, componentType: ComponentType): ComponentProps {
    const props: ComponentProps = {};

    switch (componentType) {
      case ComponentType.TEXT:
      case ComponentType.HEADING:
        if (node.characters) {
          props.text = node.characters;
        }
        break;
      case ComponentType.BUTTON:
        if (node.characters) {
          props.text = node.characters;
        }
        props.onClick = 'handleClick';
        break;
      case ComponentType.INPUT:
        props.type = 'text';
        props.placeholder = node.name || 'Enter text';
        break;
      case ComponentType.IMAGE:
        props.src = '/placeholder-image.jpg';
        props.alt = node.name || 'Image';
        break;
      case ComponentType.LINK:
        props.href = '#';
        if (node.characters) {
          props.text = node.characters;
        }
        break;
    }

    return props;
  }

  private colorToCSS(color: FigmaColor): string {
    const r = Math.round(color.r * 255);
    const g = Math.round(color.g * 255);
    const b = Math.round(color.b * 255);
    const a = color.a;

    if (a === 1) {
      return `rgb(${r}, ${g}, ${b})`;
    } else {
      return `rgba(${r}, ${g}, ${b}, ${a})`;
    }
  }

  private generateComponentId(name: string): string {
    const sanitized = this.sanitizeComponentName(name);
    return `${sanitized}_${++this.componentCounter}`;
  }

  private sanitizeComponentName(name: string): string {
    return name
      .replace(/[^a-zA-Z0-9]/g, '_')
      .replace(/^[0-9]/, '_$&')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')
      || 'Component';
  }
}
