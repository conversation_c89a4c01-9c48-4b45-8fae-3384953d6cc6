import { NextRequest, NextResponse } from 'next/server';
import { FigmaClient } from '@/lib/figma-client';
import { DesignParser } from '@/lib/design-parser';
import { CodeGenerator, CodeGenerationOptions } from '@/lib/code-generator';
import { ImageAssetManager } from '@/lib/image-asset-manager';
import { Validator, ValidationError, FigmaApiError, CodeGenerationError, createErrorResponse } from '@/lib/validation';
import JSZip from 'jszip';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { figmaUrl, figmaToken, options } = body;

    // Validate Figma URL
    const urlValidation = Validator.validateFigmaUrl(figmaUrl);
    if (!urlValidation.isValid) {
      throw new ValidationError(
        urlValidation.errors.join(', '),
        'INVALID_FIGMA_URL',
        { errors: urlValidation.errors, warnings: urlValidation.warnings }
      );
    }

    // Validate Figma token
    const tokenValidation = Validator.validateFigmaToken(figmaToken);
    if (!tokenValidation.isValid) {
      throw new ValidationError(
        tokenValidation.errors.join(', '),
        'INVALID_FIGMA_TOKEN',
        { errors: tokenValidation.errors, warnings: tokenValidation.warnings }
      );
    }

    // Validate generation options
    const optionsValidation = Validator.validateGenerationOptions(options);
    if (!optionsValidation.isValid) {
      throw new ValidationError(
        optionsValidation.errors.join(', '),
        'INVALID_OPTIONS',
        { errors: optionsValidation.errors, warnings: optionsValidation.warnings }
      );
    }

    // Extract file key from Figma URL
    const urlInfo = Validator.parseFigmaUrl(figmaUrl);
    const fileKey = urlInfo.fileId;

    console.log(`Processing Figma file: ${fileKey}`);
    if (urlValidation.warnings.length > 0) {
      console.warn('URL warnings:', urlValidation.warnings);
    }
    if (tokenValidation.warnings.length > 0) {
      console.warn('Token warnings:', tokenValidation.warnings);
    }

    // Initialize Figma client
    const figmaClient = new FigmaClient(figmaToken);

    // Fetch Figma file with error handling
    console.log('Fetching Figma file...');
    let figmaFile;
    try {
      figmaFile = await figmaClient.getFile(fileKey);
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new FigmaApiError(
          'Invalid Figma access token or insufficient permissions',
          403,
          error.response
        );
      } else if (error.response?.status === 404) {
        throw new FigmaApiError(
          'Figma file not found or not accessible',
          404,
          error.response
        );
      } else if (error.response?.status === 401) {
        throw new FigmaApiError(
          'Invalid Figma access token',
          401,
          error.response
        );
      } else {
        throw new FigmaApiError(
          'Failed to connect to Figma API',
          500,
          error.response
        );
      }
    }

    if (!figmaFile || !figmaFile.document) {
      throw new FigmaApiError(
        'Figma file is empty or has no content',
        400,
        { fileKey }
      );
    }

    console.log(`Successfully fetched Figma file: ${figmaFile.name}`);

    // Validate file size (rough estimate based on node count)
    const nodeCount = countNodes(figmaFile.document);
    const estimatedSize = nodeCount * 1000; // Rough estimate
    const sizeValidation = Validator.validateFileSize(estimatedSize);
    if (!sizeValidation.isValid) {
      throw new ValidationError(
        sizeValidation.errors.join(', '),
        'FILE_TOO_LARGE',
        { nodeCount, estimatedSize }
      );
    }
    if (sizeValidation.warnings.length > 0) {
      console.warn('File size warnings:', sizeValidation.warnings);
    }

    // Parse design
    console.log('Parsing design...');
    const parser = new DesignParser();
    const parsedComponent = parser.parse(figmaFile.document);

    // Extract and process images
    console.log('Processing images...');
    const imageManager = new ImageAssetManager(figmaClient, {
      maxWidth: 1920,
      maxHeight: 1080,
      quality: 85,
      format: 'webp',
      generateWebP: true,
      generateResponsive: true,
    });

    const imageAssets = await imageManager.extractAndProcessImages(fileKey, parsedComponent);
    console.log(`Extracted ${imageAssets.length} image assets`);

    // Update component image paths
    imageManager.updateComponentImagePaths(parsedComponent, 'assets/images');

    // Generate code
    console.log('Generating code...');
    const codeGenerationOptions: CodeGenerationOptions = {
      framework: options?.framework || 'react',
      styling: options?.styling || 'css',
      typescript: options?.typescript ?? true,
      responsive: options?.responsive ?? true,
      accessibility: options?.accessibility ?? true,
      projectName: options?.projectName || 'figma-generated-project',
    };

    const codeGenerator = new CodeGenerator(codeGenerationOptions);
    const generatedProject = await codeGenerator.generateProject(parsedComponent);

    // Create ZIP file
    console.log('Creating project archive...');
    const zip = new JSZip();

    // Add all generated files to ZIP
    for (const file of generatedProject.files) {
      zip.file(file.path, file.content);
    }

    // Add image assets to ZIP
    if (imageAssets.length > 0) {
      const assetsFolder = zip.folder('public/assets/images');

      for (const asset of imageAssets) {
        if (assetsFolder) {
          assetsFolder.file(asset.name, asset.buffer);
        }

        // Generate responsive variants if enabled
        if (codeGenerationOptions.responsive) {
          const variants = await imageManager.generateResponsiveImages(asset);
          for (const variant of variants) {
            if (variant.id !== asset.id && assetsFolder) {
              assetsFolder.file(variant.name, variant.buffer);
            }
          }
        }
      }
    }

    // Add README
    const readmeContent = generateReadme(generatedProject, codeGenerationOptions);
    zip.file('README.md', readmeContent);

    // Generate ZIP buffer
    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

    // Return ZIP file
    return new NextResponse(zipBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/zip',
        'Content-Disposition': `attachment; filename="${codeGenerationOptions.projectName}.zip"`,
      },
    });

  } catch (error: any) {
    console.error('Error generating code:', error);

    const errorResponse = createErrorResponse(error);
    const statusCode = error instanceof ValidationError ? 400 :
                      error instanceof FigmaApiError ? error.statusCode :
                      error instanceof CodeGenerationError ? 500 : 500;

    return NextResponse.json(errorResponse, { status: statusCode });
  }
}

// Helper function to count nodes in Figma document
function countNodes(node: any): number {
  let count = 1;
  if (node.children) {
    for (const child of node.children) {
      count += countNodes(child);
    }
  }
  return count;
}

function generateReadme(generatedProject: { structure: { components: string[] }, dependencies: Record<string, string>, devDependencies: Record<string, string> }, options: CodeGenerationOptions): string {
  return `# ${options.projectName}

This project was generated from a Figma design using Figma Agent.

## Project Structure

- **Framework**: ${options.framework}
- **Styling**: ${options.styling}
- **TypeScript**: ${options.typescript ? 'Yes' : 'No'}
- **Responsive**: ${options.responsive ? 'Yes' : 'No'}
- **Accessibility**: ${options.accessibility ? 'Yes' : 'No'}

## Generated Components

${generatedProject.structure.components.map((comp: string) => `- ${comp}`).join('\n')}

## Getting Started

1. Install dependencies:
   \`\`\`bash
   npm install
   \`\`\`

2. Start the development server:
   \`\`\`bash
   npm start
   \`\`\`

3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## Dependencies

### Production Dependencies
${Object.entries(generatedProject.dependencies).map(([name, version]) => `- ${name}@${version}`).join('\n')}

### Development Dependencies
${Object.entries(generatedProject.devDependencies).map(([name, version]) => `- ${name}@${version}`).join('\n')}

## Notes

- This is a generated project based on your Figma design
- You may need to adjust styles and add functionality as needed
- Images are placeholder and should be replaced with actual assets
- Interactive elements may need additional event handlers

## Support

For issues with the generated code, please check:
1. Component structure and naming
2. CSS styles and responsive behavior
3. TypeScript types (if enabled)

Generated by Figma Agent - Transform your designs into code!
`;
}
