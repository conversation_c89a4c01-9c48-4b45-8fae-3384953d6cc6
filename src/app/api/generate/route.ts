import { NextRequest, NextResponse } from 'next/server';
import { FigmaClient } from '@/lib/figma-client';
import { DesignParser } from '@/lib/design-parser';
import { CodeGenerator, CodeGenerationOptions } from '@/lib/code-generator';
import J<PERSON><PERSON><PERSON> from 'jszip';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { figmaUrl, figmaToken, options } = body;

    // Validate required fields
    if (!figmaUrl || !figmaToken) {
      return NextResponse.json(
        { error: 'Figma URL and access token are required' },
        { status: 400 }
      );
    }

    // Extract file key from Figma URL
    const fileKey = FigmaClient.extractFileKey(figmaUrl);
    if (!fileKey) {
      return NextResponse.json(
        { error: 'Invalid Figma URL format' },
        { status: 400 }
      );
    }

    // Check if it's a community file
    if (figmaUrl.includes('/community/file/')) {
      return NextResponse.json(
        { error: 'Community files cannot be accessed via API. Please duplicate the file to your account and use the duplicated file URL instead.' },
        { status: 400 }
      );
    }

    // Initialize Figma client
    const figmaClient = new FigmaClient(figmaToken);

    // Fetch Figma file
    console.log('Fetching Figma file...');
    const figmaFile = await figmaClient.getFile(fileKey);

    if (!figmaFile || !figmaFile.document) {
      return NextResponse.json(
        { error: 'Failed to fetch Figma file or file is empty' },
        { status: 400 }
      );
    }

    // Parse design
    console.log('Parsing design...');
    const parser = new DesignParser();
    const parsedComponent = parser.parse(figmaFile.document);

    // Generate code
    console.log('Generating code...');
    const codeGenerationOptions: CodeGenerationOptions = {
      framework: options?.framework || 'react',
      styling: options?.styling || 'css',
      typescript: options?.typescript ?? true,
      responsive: options?.responsive ?? true,
      accessibility: options?.accessibility ?? true,
      projectName: options?.projectName || 'figma-generated-project',
    };

    const codeGenerator = new CodeGenerator(codeGenerationOptions);
    const generatedProject = await codeGenerator.generateProject(parsedComponent);

    // Create ZIP file
    console.log('Creating project archive...');
    const zip = new JSZip();

    // Add all generated files to ZIP
    for (const file of generatedProject.files) {
      zip.file(file.path, file.content);
    }

    // Add README
    const readmeContent = generateReadme(generatedProject, codeGenerationOptions);
    zip.file('README.md', readmeContent);

    // Generate ZIP buffer
    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

    // Return ZIP file
    return new NextResponse(zipBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/zip',
        'Content-Disposition': `attachment; filename="${codeGenerationOptions.projectName}.zip"`,
      },
    });

  } catch (error) {
    console.error('Error generating code:', error);

    let errorMessage = 'An unexpected error occurred';
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;

      // Handle specific error types
      if (error.message.includes('status code 403')) {
        statusCode = 403;
        errorMessage = 'Invalid Figma access token or insufficient permissions';
      } else if (error.message.includes('status code 404')) {
        statusCode = 404;
        errorMessage = 'Figma file not found or not accessible';
      } else if (error.message.includes('status code 401')) {
        statusCode = 401;
        errorMessage = 'Invalid Figma access token';
      } else if (error.message.includes('Figma API')) {
        statusCode = 400;
        errorMessage = 'Failed to connect to Figma API';
      }
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}

function generateReadme(generatedProject: { structure: { components: string[] }, dependencies: string[], devDependencies: string[] }, options: CodeGenerationOptions): string {
  return `# ${options.projectName}

This project was generated from a Figma design using Figma Agent.

## Project Structure

- **Framework**: ${options.framework}
- **Styling**: ${options.styling}
- **TypeScript**: ${options.typescript ? 'Yes' : 'No'}
- **Responsive**: ${options.responsive ? 'Yes' : 'No'}
- **Accessibility**: ${options.accessibility ? 'Yes' : 'No'}

## Generated Components

${generatedProject.structure.components.map((comp: string) => `- ${comp}`).join('\n')}

## Getting Started

1. Install dependencies:
   \`\`\`bash
   npm install
   \`\`\`

2. Start the development server:
   \`\`\`bash
   npm start
   \`\`\`

3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## Dependencies

### Production Dependencies
${generatedProject.dependencies.map((dep: string) => `- ${dep}`).join('\n')}

### Development Dependencies
${generatedProject.devDependencies.map((dep: string) => `- ${dep}`).join('\n')}

## Notes

- This is a generated project based on your Figma design
- You may need to adjust styles and add functionality as needed
- Images are placeholder and should be replaced with actual assets
- Interactive elements may need additional event handlers

## Support

For issues with the generated code, please check:
1. Component structure and naming
2. CSS styles and responsive behavior
3. TypeScript types (if enabled)

Generated by Figma Agent - Transform your designs into code!
`;
}
