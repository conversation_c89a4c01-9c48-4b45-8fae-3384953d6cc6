'use client';

import { useState } from 'react';
import { Download, Upload, Settings, Loader2, CheckCircle, AlertCircle } from 'lucide-react';

interface GenerationOptions {
  framework: 'react' | 'vue' | 'angular';
  styling: 'css' | 'scss' | 'tailwind' | 'styled-components';
  typescript: boolean;
  responsive: boolean;
  accessibility: boolean;
  projectName: string;
}

export default function Home() {
  const [figmaUrl, setFigmaUrl] = useState('');
  const [figmaToken, setFigmaToken] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const [generationStatus, setGenerationStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const [options, setOptions] = useState<GenerationOptions>({
    framework: 'react',
    styling: 'tailwind',
    typescript: true,
    responsive: true,
    accessibility: true,
    projectName: 'figma-generated-project',
  });

  const handleGenerate = async () => {
    if (!figmaUrl || !figmaToken) {
      setErrorMessage('Please provide both Figma URL and access token');
      setGenerationStatus('error');
      return;
    }

    setIsGenerating(true);
    setGenerationStatus('idle');
    setErrorMessage('');

    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          figmaUrl,
          figmaToken,
          options,
        }),
      });

      if (!response.ok) {
        let errorMessage = 'Failed to generate code';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch {
          // If JSON parsing fails, use default message
          errorMessage = `Server error (${response.status}): ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      // Download the ZIP file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${options.projectName}.zip`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setGenerationStatus('success');
    } catch (error) {
      console.error('Generation failed:', error);
      setErrorMessage(error instanceof Error ? error.message : 'An unexpected error occurred');
      setGenerationStatus('error');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-4">
            Figma Agent
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Transform your Figma designs into production-ready code instantly.
            Upload your design file and get a complete project with components, styles, and structure.
          </p>
        </div>

        {/* Main Form */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
            <div className="space-y-6">
              {/* Figma URL Input */}
              <div>
                <label htmlFor="figma-url" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Figma File URL
                </label>
                <input
                  id="figma-url"
                  type="url"
                  value={figmaUrl}
                  onChange={(e) => setFigmaUrl(e.target.value)}
                  placeholder="https://www.figma.com/file/..."
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>

              {/* Figma Token Input */}
              <div>
                <label htmlFor="figma-token" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Figma Access Token
                </label>
                <input
                  id="figma-token"
                  type="password"
                  value={figmaToken}
                  onChange={(e) => setFigmaToken(e.target.value)}
                  placeholder="Your Figma personal access token"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Get your token from{' '}
                  <a
                    href="https://www.figma.com/developers/api#access-tokens"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline"
                  >
                    Figma Settings
                  </a>
                </p>
              </div>

              {/* Options Toggle */}
              <div>
                <button
                  onClick={() => setShowOptions(!showOptions)}
                  className="flex items-center gap-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                >
                  <Settings size={20} />
                  Advanced Options
                </button>
              </div>

              {/* Advanced Options */}
              {showOptions && (
                <div className="border-t border-gray-200 dark:border-gray-600 pt-6 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Framework */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Framework
                      </label>
                      <select
                        value={options.framework}
                        onChange={(e) => setOptions({...options, framework: e.target.value as 'react' | 'vue' | 'angular'})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                      >
                        <option value="react">React</option>
                        <option value="vue">Vue</option>
                        <option value="angular">Angular</option>
                      </select>
                    </div>

                    {/* Styling */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Styling
                      </label>
                      <select
                        value={options.styling}
                        onChange={(e) => setOptions({...options, styling: e.target.value as 'css' | 'scss' | 'tailwind' | 'styled-components'})}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                      >
                        <option value="tailwind">Tailwind CSS</option>
                        <option value="css">CSS</option>
                        <option value="scss">SCSS</option>
                        <option value="styled-components">Styled Components</option>
                      </select>
                    </div>
                  </div>

                  {/* Project Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Project Name
                    </label>
                    <input
                      type="text"
                      value={options.projectName}
                      onChange={(e) => setOptions({...options, projectName: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  {/* Checkboxes */}
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={options.typescript}
                        onChange={(e) => setOptions({...options, typescript: e.target.checked})}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Use TypeScript</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={options.responsive}
                        onChange={(e) => setOptions({...options, responsive: e.target.checked})}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Responsive Design</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={options.accessibility}
                        onChange={(e) => setOptions({...options, accessibility: e.target.checked})}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Accessibility Features</span>
                    </label>
                  </div>
                </div>
              )}

              {/* Status Messages */}
              {generationStatus === 'success' && (
                <div className="flex items-center gap-2 text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                  <CheckCircle size={20} />
                  <span>Project generated successfully! Check your downloads.</span>
                </div>
              )}

              {generationStatus === 'error' && (
                <div className="flex items-center gap-2 text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
                  <AlertCircle size={20} />
                  <span>{errorMessage}</span>
                </div>
              )}

              {/* Generate Button */}
              <button
                onClick={handleGenerate}
                disabled={isGenerating || !figmaUrl || !figmaToken}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors flex items-center justify-center gap-2"
              >
                {isGenerating ? (
                  <>
                    <Loader2 size={20} className="animate-spin" />
                    Generating Code...
                  </>
                ) : (
                  <>
                    <Download size={20} />
                    Generate Code
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="bg-blue-100 dark:bg-blue-900/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Upload className="text-blue-600 dark:text-blue-400" size={24} />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Easy Upload</h3>
            <p className="text-gray-600 dark:text-gray-300">
              Simply paste your Figma URL and access token to get started
            </p>
          </div>

          <div className="text-center">
            <div className="bg-green-100 dark:bg-green-900/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Settings className="text-green-600 dark:text-green-400" size={24} />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Customizable</h3>
            <p className="text-gray-600 dark:text-gray-300">
              Choose your preferred framework, styling approach, and features
            </p>
          </div>

          <div className="text-center">
            <div className="bg-purple-100 dark:bg-purple-900/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Download className="text-purple-600 dark:text-purple-400" size={24} />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Ready to Use</h3>
            <p className="text-gray-600 dark:text-gray-300">
              Download a complete project with components, styles, and structure
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
