# Figma Agent 🎨➡️💻

Transform your Figma designs into production-ready code instantly! Figma Agent is an AI-powered tool that analyzes your Figma design files and generates complete, structured code projects with components, styles, and proper architecture.

## ✨ Features

- **🔄 Instant Conversion**: Convert Figma designs to code in seconds
- **🎯 Multiple Frameworks**: Support for React, Vue, and Angular
- **🎨 Flexible Styling**: Choose from CSS, SCSS, Tailwind CSS, or Styled Components
- **📱 Responsive Design**: Generate mobile-first, responsive layouts
- **♿ Accessibility**: Built-in accessibility features and ARIA attributes
- **🔧 TypeScript Support**: Optional TypeScript generation for type safety
- **📦 Complete Projects**: Get full project structure with package.json, configs, and more
- **🎪 Component Architecture**: Intelligent component extraction and hierarchy
- **🤖 AI-Enhanced Generation**: Powered by MyGenAssist for smarter code generation
- **🎯 Code Optimization**: Automatic code optimization and best practices

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm
- A Figma account with API access
- Figma Personal Access Token
- MyGenAssist API Token (optional, for enhanced AI features)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/figma-agent.git
cd figma-agent
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
# Edit .env.local with your configuration
```

4. Start the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

## 🔑 Getting Your Figma Access Token

1. Go to [Figma Settings](https://www.figma.com/settings)
2. Scroll down to "Personal access tokens"
3. Click "Create new token"
4. Give it a name and click "Create"
5. Copy the token (you won't see it again!)

## 📖 How to Use

### Web Interface

1. **Enter Figma URL**: Paste your Figma file URL (e.g., `https://www.figma.com/file/abc123/My-Design`)
2. **Add Access Token**: Enter your Figma personal access token
3. **Configure Options** (optional):
   - Choose framework (React, Vue, Angular)
   - Select styling approach (Tailwind, CSS, SCSS, Styled Components)
   - Enable TypeScript, responsive design, accessibility features
   - Set project name
4. **Generate**: Click "Generate Code" and download your project

### API Usage

You can also use the API directly:

```bash
curl -X POST http://localhost:3000/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "figmaUrl": "https://www.figma.com/file/your-file-id/Your-Design",
    "figmaToken": "your-figma-token",
    "options": {
      "framework": "react",
      "styling": "tailwind",
      "typescript": true,
      "responsive": true,
      "accessibility": true,
      "projectName": "my-project"
    }
  }'
```

## 🏗️ Architecture

### Core Components

- **Figma Client** (`src/lib/figma-client.ts`): Handles Figma API communication
- **Design Parser** (`src/lib/design-parser.ts`): Analyzes and structures design data
- **Code Generator** (`src/lib/code-generator.ts`): Converts parsed data to code
- **API Routes** (`src/app/api/generate/route.ts`): Handles generation requests
- **Web Interface** (`src/app/page.tsx`): User-friendly frontend

### Data Flow

1. **Fetch**: Retrieve design data from Figma API
2. **Parse**: Analyze components, styles, and layout
3. **Generate**: Create code files with proper structure
4. **Package**: Bundle everything into a downloadable ZIP

## 🎛️ Configuration Options

### Framework Support

- **React**: Modern React with hooks and functional components
- **Vue**: Vue 3 with Composition API
- **Angular**: Angular with TypeScript and modern practices

### Styling Options

- **Tailwind CSS**: Utility-first CSS framework
- **CSS**: Standard CSS with BEM methodology
- **SCSS**: Sass with nested styles and variables
- **Styled Components**: CSS-in-JS for React

### Features

- **TypeScript**: Type-safe code generation
- **Responsive Design**: Mobile-first responsive layouts
- **Accessibility**: ARIA attributes and semantic HTML
- **Component Architecture**: Intelligent component extraction

## 📁 Generated Project Structure

```
my-project/
├── package.json
├── tsconfig.json (if TypeScript)
├── tailwind.config.js (if Tailwind)
├── src/
│   ├── components/
│   │   ├── Button.tsx
│   │   ├── Header.tsx
│   │   └── ...
│   ├── App.tsx
│   ├── index.tsx
│   └── index.css
└── README.md
```

## 🔧 Development

### Project Structure

```
figma-agent/
├── src/
│   ├── app/
│   │   ├── api/generate/route.ts
│   │   ├── page.tsx
│   │   └── layout.tsx
│   └── lib/
│       ├── figma-client.ts
│       ├── design-parser.ts
│       └── code-generator.ts
├── package.json
└── README.md
```

### Available Scripts

- `npm run dev`: Start development server
- `npm run build`: Build for production
- `npm run start`: Start production server
- `npm run lint`: Run ESLint

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Figma API](https://www.figma.com/developers/api) for design data access
- [Next.js](https://nextjs.org/) for the web framework
- [Tailwind CSS](https://tailwindcss.com/) for styling
- [Lucide React](https://lucide.dev/) for icons

## 🐛 Troubleshooting

### Common Issues

**"Invalid Figma URL format"**
- Ensure your URL follows the format: `https://www.figma.com/file/FILE_ID/FILE_NAME`

**"Invalid Figma access token"**
- Check that your token is valid and has the necessary permissions
- Make sure you're using a Personal Access Token, not a team token

**"Failed to fetch Figma file"**
- Verify the file is accessible with your token
- Check if the file is in a team you have access to

**"Generation failed"**
- Try with a simpler design first
- Check browser console for detailed error messages

### Getting Help

- Check the [Issues](https://github.com/your-username/figma-agent/issues) page
- Create a new issue with detailed information about your problem
- Include your Figma file structure and error messages

---

Made with ❤️ by the Figma Agent team
