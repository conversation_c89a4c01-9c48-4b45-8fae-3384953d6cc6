{"name": "figma-agent", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@types/archiver": "^6.0.3", "@types/multer": "^2.0.0", "archiver": "^7.0.1", "axios": "^1.10.0", "figma-api": "^2.0.2-beta", "jszip": "^3.10.1", "lucide-react": "^0.525.0", "multer": "^2.0.1", "next": "15.3.5", "openai": "^5.9.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "tailwindcss": "^4", "ts-jest": "^29.4.0", "typescript": "^5"}}